import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { <PERSON>rowserRouter } from 'react-router-dom';
import WireframePage from '../WireframePage';

// Mock the router params
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ projectId: 'test-project' })
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock toast functions
jest.mock('../../utils/toast', () => ({
  showSuccess: jest.fn(),
  showError: jest.fn()
}));

// Wrapper component for tests
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <DndProvider backend={HTML5Backend}>
      {children}
    </DndProvider>
  </BrowserRouter>
);

describe('WireframePage Integration Tests', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  test('renders wireframe page with default elements', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    expect(screen.getByText('Wireframe')).toBeInTheDocument();
    expect(screen.getByText('Project: test-project')).toBeInTheDocument();
    expect(screen.getByText('Import to Flowchart')).toBeInTheDocument();
  });

  test('shows component toolbar with categories', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Layouts')).toBeInTheDocument();
    expect(screen.getByText('Form Controls')).toBeInTheDocument();
    expect(screen.getByText('Navigation')).toBeInTheDocument();
  });

  test('filters components by category', async () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    // Click on Form Controls category
    fireEvent.click(screen.getByText('Form Controls'));

    await waitFor(() => {
      expect(screen.getByText('Button')).toBeInTheDocument();
      expect(screen.getByText('Text Input')).toBeInTheDocument();
      // Should not show layout components
      expect(screen.queryByText('Desktop Browser')).not.toBeInTheDocument();
    });
  });

  test('shows grid and snap controls', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    expect(screen.getByText('Grid')).toBeInTheDocument();
    expect(screen.getByText('Snap')).toBeInTheDocument();
    expect(screen.getByText(/Zoom:/)).toBeInTheDocument();
  });

  test('toggles grid visibility', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    const gridButton = screen.getByText('Grid');
    
    // Grid should be active by default
    expect(gridButton).toHaveClass('bg-blue-500');
    
    // Click to toggle
    fireEvent.click(gridButton);
    
    expect(gridButton).toHaveClass('bg-white');
  });

  test('toggles grid snapping', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    const snapButton = screen.getByText('Snap');
    
    // Snap should be active by default
    expect(snapButton).toHaveClass('bg-blue-500');
    
    // Click to toggle
    fireEvent.click(snapButton);
    
    expect(snapButton).toHaveClass('bg-white');
  });

  test('shows keyboard shortcuts help', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    const helpButton = screen.getByText('?');
    fireEvent.click(helpButton);

    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument();
    expect(screen.getByText('Toggle grid')).toBeInTheDocument();
    expect(screen.getByText('Toggle grid snapping')).toBeInTheDocument();
  });

  test('handles imported flowchart data', () => {
    const importedData = {
      pages: [
        {
          id: 'page1',
          name: 'Login Page',
          components: [
            {
              id: 'comp1',
              type: 'Text',
              x: 50,
              y: 50,
              width: 200,
              height: 40,
              text: 'Welcome to Login Page',
              data: { placeholder: true }
            }
          ]
        }
      ]
    };

    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'importedWireframeData') {
        return JSON.stringify(importedData);
      }
      return '[]';
    });

    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    expect(screen.getByText('Login Page')).toBeInTheDocument();
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('importedWireframeData');
  });

  test('persists data to localStorage', async () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    // Wait for initial effects to complete
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'wireframe_pages_test-project',
        expect.any(String)
      );
    });
  });

  test('handles zoom controls via keyboard', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    // Initial zoom should be 100%
    expect(screen.getByText('Zoom: 100%')).toBeInTheDocument();

    // Simulate Ctrl++ for zoom in
    fireEvent.keyDown(document, { key: '+', ctrlKey: true });
    
    expect(screen.getByText('Zoom: 110%')).toBeInTheDocument();

    // Simulate Ctrl+- for zoom out
    fireEvent.keyDown(document, { key: '-', ctrlKey: true });
    
    expect(screen.getByText('Zoom: 100%')).toBeInTheDocument();
  });

  test('shows panels toggle buttons', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    expect(screen.getByText('Hide Pages')).toBeInTheDocument();
    expect(screen.getByText('Hide Props')).toBeInTheDocument();
  });

  test('toggles left panel visibility', () => {
    localStorageMock.getItem.mockReturnValue('[]');
    
    render(
      <TestWrapper>
        <WireframePage />
      </TestWrapper>
    );

    const toggleButton = screen.getByText('Hide Pages');
    fireEvent.click(toggleButton);

    expect(screen.getByText('Show Pages')).toBeInTheDocument();
  });

  test('handles error in imported data gracefully', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'importedWireframeData') {
        return 'invalid json';
      }
      return '[]';
    });

    // Should not throw error
    expect(() => {
      render(
        <TestWrapper>
          <WireframePage />
        </TestWrapper>
      );
    }).not.toThrow();

    expect(localStorageMock.removeItem).toHaveBeenCalledWith('importedWireframeData');
  });
});
