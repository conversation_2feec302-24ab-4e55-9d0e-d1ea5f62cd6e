import { use<PERSON>ara<PERSON> } from "react-router-dom";
import { WireframeToolbar } from "@/components/wireframe/WireframeToolbar";
import { WireframePagesPanel, WireframePageItem } from "@/components/wireframe/WireframePagesPanel";
import { WireframePropertiesPanel, WireframeComponent } from "@/components/wireframe/WireframePropertiesPanel";
import { ComponentDataModal } from "@/components/wireframe/ComponentDataModal";
import { Button } from "@/components/ui/button";
import { Download, Upload, ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import { showSuccess, showError } from "@/utils/toast";
import React, { useState, useRef, useCallback, useEffect } from "react";
import { useDrop, XYCoord } from "react-dnd";
import { GridOverlay, snapToGrid } from "@/components/wireframe/GridOverlay";
import { DropZoneIndicator } from "@/components/wireframe/DropZoneIndicator";
import { PositionIndicator } from "@/components/wireframe/PositionIndicator";
import { KeyboardShortcuts, KeyboardShortcutsHelp } from "@/components/wireframe/KeyboardShortcuts";
import { WireframeErrorBoundary } from "@/components/wireframe/ErrorBoundary";
import { AccessibilityEnhancements, AriaLiveRegion, SkipLink } from "@/components/wireframe/AccessibilityEnhancements";
import { HelpDocumentation } from "@/components/wireframe/HelpDocumentation";
import { ComponentItem, DndComponentItemPayload, DndToolbarItemPayload, DragItem } from "@/components/wireframe/ComponentItem";
import { ResizableComponent } from "@/components/wireframe/ResizableComponent";
import { HistoryProvider, useHistoryContext, useHistoryKeyboardShortcuts } from "@/contexts/HistoryContext";
import { createAction, ACTION_TYPES } from "@/hooks/useHistory";

const WireframePageContent = () => {
  const { projectId } = useParams<{ projectId:string }>();
  const { addAction, addDebouncedAction } = useHistoryContext();

  console.log('WireframePageContent initialized with projectId:', projectId);

  // Enable keyboard shortcuts for undo/redo
  useHistoryKeyboardShortcuts();

  // State to hold all pages and their respective components
  const [allPagesData, setAllPagesData] = useState<Record<string, WireframeComponent[]>>({});
  const [pages, setPages] = useState<WireframePageItem[]>([]);
  const [activePageId, setActivePageId] = useState<string | null>(null);

  const [selectedComponent, setSelectedComponent] = useState<WireframeComponent | null>(null);
  const [showLeftPanel, setShowLeftPanel] = useState(true);
  const [showRightPanel, setShowRightPanel] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [isDataModalOpen, setIsDataModalOpen] = useState(false);
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);
  const [showHelpDocumentation, setShowHelpDocumentation] = useState(false);

  const canvasRef = useRef<HTMLDivElement>(null);
  const canvasDropTargetRef = useRef<HTMLDivElement>(null);

  // Enhanced drag and drop state
  const [dragState, setDragState] = useState({
    showGrid: false, // Only show when dragging
    gridSnapping: false, // Disabled by default for smooth movement
    gridSize: 20,
    showDropZone: false,
    dropZone: { x: 0, y: 0, width: 0, height: 0, type: 'valid' as 'valid' | 'invalid' },
    showPosition: false,
    position: { x: 0, y: 0, width: 0, height: 0 },
    isDragging: false // Track dragging state
  });

  // Initialize pages and activePageId when projectId changes
  useEffect(() => {
    const effectiveProjectId = projectId || 'default-project';
    console.log('Initializing with projectId:', effectiveProjectId);
    if (effectiveProjectId) {
      // For demonstration, we'll use a simple in-memory store per project.
      // In a real app, you'd load this from local storage or a backend.
      const storedPages = JSON.parse(localStorage.getItem(`wireframe_pages_${effectiveProjectId}`) || '[]');
      const storedAllPagesData = JSON.parse(localStorage.getItem(`wireframe_data_${effectiveProjectId}`) || '{}');

      if (storedPages.length === 0) {
        // If no pages exist for this project, create a default one
        const defaultPageId = `page-${Date.now()}`;
        const defaultPage: WireframePageItem = { id: defaultPageId, name: "Page 1" };
        setPages([defaultPage]);
        setAllPagesData({ [defaultPageId]: [] });
        setActivePageId(defaultPageId);
        showSuccess(`Project "${decodeURIComponent(effectiveProjectId)}" created with a default page.`);
      } else {
        setPages(storedPages);
        setAllPagesData(storedAllPagesData);
        // Set active page to the first one if none is active or if the active one was deleted
        if (!activePageId || !storedPages.some((p: WireframePageItem) => p.id === activePageId)) {
          setActivePageId(storedPages[0]?.id || null);
        }
      }
    }
  }, [projectId]);

  // Persist pages and allPagesData to local storage whenever they change
  useEffect(() => {
    const effectiveProjectId = projectId || 'default-project';
    localStorage.setItem(`wireframe_pages_${effectiveProjectId}`, JSON.stringify(pages));
    localStorage.setItem(`wireframe_data_${effectiveProjectId}`, JSON.stringify(allPagesData));
  }, [pages, allPagesData, projectId]);

  // Check for imported flowchart data on component mount
  useEffect(() => {
    const importedDataString = localStorage.getItem('importedWireframeData');
    if (importedDataString) {
      try {
        const importedData: WireframeImportData = JSON.parse(importedDataString);

        if (importedData.pages && importedData.pages.length > 0) {
          // Convert imported pages to the format expected by this component
          const newPages: WireframePageItem[] = importedData.pages.map(page => ({
            id: page.id,
            name: page.name
          }));

          const newPagesData: Record<string, WireframeComponent[]> = {};
          importedData.pages.forEach(page => {
            newPagesData[page.id] = page.components;
          });

          // Set the imported data
          setPages(newPages);
          setAllPagesData(newPagesData);

          // Select the first imported page
          if (newPages.length > 0) {
            setActivePageId(newPages[0].id);
          }

          // Clear the imported data from localStorage
          localStorage.removeItem('importedWireframeData');

          console.log('Successfully loaded imported flowchart data:', importedData);
        }
      } catch (error) {
        console.error('Error parsing imported wireframe data:', error);
        localStorage.removeItem('importedWireframeData');
      }
    }
  }, []); // Run only once on mount

  // Check for auto-synced flowchart data periodically
  useEffect(() => {
    const checkAutoSyncData = () => {
      const autoSyncDataString = localStorage.getItem('autoSyncWireframeData');
      if (autoSyncDataString) {
        try {
          const autoSyncData: WireframeImportData = JSON.parse(autoSyncDataString);

          if (autoSyncData.pages && autoSyncData.pages.length > 0) {
            // Only update if the project ID matches or if we don't have a project ID
            if (!projectId || autoSyncData.projectId === projectId) {
              // Convert auto-sync pages to the format expected by this component
              const newPages: WireframePageItem[] = autoSyncData.pages.map(page => ({
                id: page.id,
                name: page.name
              }));

              const newPagesData: Record<string, WireframeComponent[]> = {};
              autoSyncData.pages.forEach(page => {
                newPagesData[page.id] = page.components;
              });

              // Update the data
              setPages(newPages);
              setAllPagesData(newPagesData);

              // Select the first page if no page is currently active
              if (!activePageId && newPages.length > 0) {
                setActivePageId(newPages[0].id);
              }

              console.log('Auto-synced wireframe data from flowchart:', autoSyncData);
            }
          }
        } catch (error) {
          console.error('Error parsing auto-sync wireframe data:', error);
          localStorage.removeItem('autoSyncWireframeData');
        }
      }
    };

    // Check immediately
    checkAutoSyncData();

    // Set up interval to check for auto-sync data every 2 seconds
    const interval = setInterval(checkAutoSyncData, 2000);

    return () => clearInterval(interval);
  }, [projectId, activePageId]); // Re-run when projectId or activePageId changes

  // Get current components for the active page
  const currentComponents = activePageId ? (allPagesData[activePageId] || []) : [];

  // Debug: Log current components whenever they change
  useEffect(() => {
    console.log('Current components updated:', {
      activePageId,
      currentComponentsCount: currentComponents.length,
      allPagesDataKeys: Object.keys(allPagesData),
      allPagesData: allPagesData
    });
  }, [currentComponents, activePageId, allPagesData]);

  const handleAddPage = (pageName: string) => {
    const newPageId = `page-${Date.now()}`;
    const newPage: WireframePageItem = { id: newPageId, name: pageName };
    setPages((prev) => [...prev, newPage]);
    setAllPagesData((prev) => ({ ...prev, [newPageId]: [] }));
    setActivePageId(newPageId); // Automatically select the new page
  };

  const handleDeletePage = (pageId: string) => {
    setPages((prev) => prev.filter((page) => page.id !== pageId));
    setAllPagesData((prev) => {
      const newData = { ...prev };
      delete newData[pageId];
      return newData;
    });
    if (activePageId === pageId) {
      // If the deleted page was active, switch to the first remaining page or null
      setActivePageId(pages.filter(p => p.id !== pageId)[0]?.id || null);
      setSelectedComponent(null); // Clear selected component
    }
  };

  const handleSelectPage = (pageId: string) => {
    setActivePageId(pageId);
    setSelectedComponent(null); // Clear selected component when switching pages
  };

  const handleUpdatePageName = (pageId: string, newName: string) => {
    setPages((prev) =>
      prev.map((page) => (page.id === pageId ? { ...page, name: newName } : page))
    );
  };

  const handleReorderPages = (reorderedPages: WireframePageItem[]) => {
    setPages(reorderedPages);
  };

  const handleAddComponent = (componentType: string) => {
    console.log('handleAddComponent called:', { componentType, activePageId });
    if (!activePageId) {
      console.error('No active page ID!');
      return;
    }

    // Get default size for component type
    const defaultSizes: Record<string, { width: number; height: number }> = {
      'Button': { width: 120, height: 40 },
      'Text Input': { width: 200, height: 40 },
      'Text Area': { width: 300, height: 120 },
      'Navbar': { width: 800, height: 60 },
      'Sidebar': { width: 250, height: 400 },
      'Modal': { width: 400, height: 300 },
      'Table': { width: 500, height: 300 },
      'Desktop Browser': { width: 800, height: 600 },
      'Android Browser': { width: 375, height: 667 },
      'iOS Browser': { width: 375, height: 812 },
      'Image Placeholder': { width: 200, height: 150 },
      'Video Placeholder': { width: 300, height: 200 },

      // New Form Components
      'Menu Bar': { width: 300, height: 30 },
      'Multiline Button': { width: 120, height: 60 },
      'Number Stepper': { width: 100, height: 32 },
      'ON/OFF Switch': { width: 60, height: 30 },
      'Playback Controls': { width: 200, height: 40 },
      'Pointy Button': { width: 120, height: 40 },
      'Time Picker': { width: 120, height: 32 },
      'Rectangle': { width: 200, height: 100 },

      // New Layout Components
      'V Splitter': { width: 20, height: 300 },
      'V Tabs': { width: 200, height: 300 },
      'V Slider': { width: 30, height: 200 },
      'V Scroll Bar': { width: 16, height: 200 },
      'V Rule': { width: 20, height: 300 },
      'V Curly Bracket': { width: 30, height: 200 },
      'Window': { width: 400, height: 300 },

      // New Text Components
      'Text Title': { width: 300, height: 40 },
      'Text Subtitle': { width: 250, height: 30 },
      'Squiggly Line': { width: 200, height: 20 },

      // New Interactive Components
      'Red X': { width: 32, height: 32 },
      'Scratch-O': { width: 100, height: 30 },
      'Shape': { width: 100, height: 100 },
      'Site Map': { width: 300, height: 200 },
      'Smartphone': { width: 200, height: 400 },
      'Street Map': { width: 300, height: 200 },
      'Tab Bar': { width: 300, height: 50 },
      'Tag Cloud': { width: 300, height: 150 },
      'Tree Pane': { width: 250, height: 300 },
      'Video Player': { width: 400, height: 250 },
      'Volume Slider': { width: 150, height: 30 },
      'Webcam': { width: 200, height: 150 },

      // New Navigation Components
      'Toolbar': { width: 400, height: 40 },
      'Popover': { width: 200, height: 100 },
      'Progress Indicator': { width: 300, height: 30 }
    };

    const size = defaultSizes[componentType] || { width: 120, height: 40 };

    const newComponent: WireframeComponent = {
      id: Date.now().toString(),
      type: componentType,
      x: 100,
      y: 100,
      width: size.width,
      height: size.height,
      text: getDefaultText(componentType),
      data: getDefaultData(componentType),
      zIndex: getNextZIndex(componentType),
    };

    // Add component with history tracking
    const previousComponents = allPagesData[activePageId] || [];
    console.log('Adding component:', {
      componentType,
      newComponent,
      activePageId,
      previousComponentsCount: previousComponents.length
    });

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: [...previousComponents, newComponent],
    }));

    // Debug: Log the updated state
    setTimeout(() => {
      const updatedComponents = allPagesData[activePageId] || [];
      console.log('After adding component:', {
        updatedComponentsCount: updatedComponents.length,
        allComponents: updatedComponents.map(c => ({ id: c.id, type: c.type }))
      });
    }, 100);

    // Add to history
    addAction(createAction(
      ACTION_TYPES.ADD_COMPONENT,
      `Add ${componentType}`,
      () => {
        // Undo: remove the component
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).filter(c => c.id !== newComponent.id),
        }));
        if (selectedComponent?.id === newComponent.id) {
          setSelectedComponent(null);
        }
      },
      () => {
        // Redo: add the component back
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: [...(prev[activePageId] || []), newComponent],
        }));
      },
      { component: newComponent, pageId: activePageId }
    ));
  };

  const handleMoveComponent = useCallback((id: string, x: number, y: number) => {
    if (!activePageId) return;

    // Find the component to get its current position for history
    const currentComponent = (allPagesData[activePageId] || []).find(c => c.id === id);
    if (!currentComponent) return;

    const oldX = currentComponent.x;
    const oldY = currentComponent.y;

    // Apply grid snapping if enabled
    let finalX = x;
    let finalY = y;
    if (dragState.gridSnapping) {
      finalX = snapToGrid(x, dragState.gridSize);
      finalY = snapToGrid(y, dragState.gridSize);
    }

    // Only update if position actually changed
    if (oldX === finalX && oldY === finalY) return;

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((component) =>
        component.id === id ? { ...component, x: finalX, y: finalY } : component
      ),
    }));

    // Add debounced history action for move operations
    addDebouncedAction(createAction(
      ACTION_TYPES.MOVE_COMPONENT,
      `Move ${currentComponent.type}`,
      () => {
        // Undo: restore original position
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).map((component) =>
            component.id === id ? { ...component, x: oldX, y: oldY } : component
          ),
        }));
      },
      () => {
        // Redo: apply new position
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).map((component) =>
            component.id === id ? { ...component, x: finalX, y: finalY } : component
          ),
        }));
      },
      { componentId: id, oldPosition: { x: oldX, y: oldY }, newPosition: { x: finalX, y: finalY } }
    ));
  }, [activePageId, dragState.gridSnapping, dragState.gridSize, allPagesData, addDebouncedAction]);

  const handleResizeComponent = useCallback((id: string, width: number, height: number) => {
    if (!activePageId) return;

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((component) =>
        component.id === id ? { ...component, width, height } : component
      ),
    }));
  }, [activePageId]);

  // Define layout components with different z-index priorities
  const backgroundLayoutComponents = ['Desktop Browser']; // Always stay in background
  const overlayLayoutComponents = ['Android Browser', 'iOS Browser', 'Modal', 'Inspector Panel']; // Can be layered

  // Get the appropriate z-index for new components based on type
  const getNextZIndex = useCallback((componentType?: string) => {
    if (!activePageId) return 1;
    const components = allPagesData[activePageId] || [];

    // Desktop Browser gets the absolute lowest z-index (1)
    if (componentType === 'Desktop Browser') {
      return 1;
    }

    // Mobile browsers and other overlay layouts can be layered normally
    if (componentType && overlayLayoutComponents.includes(componentType)) {
      const allZIndices = components.map(c => c.zIndex || 0);
      const maxZIndex = Math.max(0, ...allZIndices);
      return maxZIndex + 1;
    }

    // Regular components get normal z-index values
    const allZIndices = components.map(c => c.zIndex || 0);
    const maxZIndex = Math.max(0, ...allZIndices);
    return maxZIndex + 1;
  }, [activePageId, allPagesData, overlayLayoutComponents]);

  // Bring component to front when selected (but respect background layout constraints)
  const bringToFront = useCallback((id: string) => {
    if (!activePageId) return;

    const component = (allPagesData[activePageId] || []).find(c => c.id === id);
    if (!component) return;

    // Only prevent Desktop Browser from being brought to front - it should stay in background
    if (backgroundLayoutComponents.includes(component.type)) {
      return;
    }

    const nextZIndex = getNextZIndex();
    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((comp) =>
        comp.id === id ? { ...comp, zIndex: nextZIndex } : comp
      ),
    }));
  }, [activePageId, getNextZIndex, backgroundLayoutComponents]);

  // Send component to back
  const sendToBack = useCallback((id: string) => {
    if (!activePageId) return;

    const component = (allPagesData[activePageId] || []).find(c => c.id === id);
    if (!component) return;

    // Prevent Desktop Browser from being sent further back
    if (backgroundLayoutComponents.includes(component.type)) {
      return;
    }

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((comp) =>
        comp.id === id ? { ...comp, zIndex: 2 } : comp // Just above Desktop Browser
      ),
    }));
  }, [activePageId, backgroundLayoutComponents]);

  // Bring component forward by one layer
  const bringForward = useCallback((id: string) => {
    if (!activePageId) return;

    const component = (allPagesData[activePageId] || []).find(c => c.id === id);
    if (!component) return;

    if (backgroundLayoutComponents.includes(component.type)) {
      return;
    }

    const currentZIndex = component.zIndex || 0;
    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((comp) =>
        comp.id === id ? { ...comp, zIndex: currentZIndex + 1 } : comp
      ),
    }));
  }, [activePageId, backgroundLayoutComponents]);

  // Send component backward by one layer
  const sendBackward = useCallback((id: string) => {
    if (!activePageId) return;

    const component = (allPagesData[activePageId] || []).find(c => c.id === id);
    if (!component) return;

    if (backgroundLayoutComponents.includes(component.type)) {
      return;
    }

    const currentZIndex = component.zIndex || 0;
    const newZIndex = Math.max(2, currentZIndex - 1); // Don't go below Desktop Browser level
    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((comp) =>
        comp.id === id ? { ...comp, zIndex: newZIndex } : comp
      ),
    }));
  }, [activePageId, backgroundLayoutComponents]);

  const [, drop] = useDrop(() => ({
    accept: ["COMPONENT", "TOOLBAR_COMPONENT"],
    hover: (item: DragItem, monitor) => {
      const currentClientOffset = monitor.getClientOffset();
      const canvasRect = canvasDropTargetRef.current?.getBoundingClientRect();

      if (currentClientOffset && canvasRect) {
        const x = (currentClientOffset.x - canvasRect.left) / zoom;
        const y = (currentClientOffset.y - canvasRect.top) / zoom;

        let componentWidth = 120;
        let componentHeight = 40;

        // Handle different types of drag items
        if ('id' in item) {
          // Existing component being moved
          const draggedComponent = currentComponents.find(c => c.id === item.id);
          if (draggedComponent) {
            componentWidth = draggedComponent.width;
            componentHeight = draggedComponent.height;
          }
        } else if ('componentType' in item) {
          // New component from toolbar - use default sizes based on component type
          const defaultSizes: Record<string, { width: number; height: number }> = {
            'Button': { width: 120, height: 40 },
            'Text Input': { width: 200, height: 40 },
            'Text Area': { width: 300, height: 120 },
            'Navbar': { width: 800, height: 60 },
            'Sidebar': { width: 250, height: 400 },
            'Modal': { width: 400, height: 300 },
            'Table': { width: 500, height: 300 },
            'Desktop Browser': { width: 800, height: 600 },
            'Android Browser': { width: 375, height: 667 },
            'iOS Browser': { width: 375, height: 812 },
            'Image Placeholder': { width: 200, height: 150 },
            'Video Placeholder': { width: 300, height: 200 },
          };

          const size = defaultSizes[item.componentType] || { width: 120, height: 40 };
          componentWidth = size.width;
          componentHeight = size.height;
        }

        let finalX = x - componentWidth / 2;
        let finalY = y - componentHeight / 2;

        // Apply grid snapping for preview
        if (dragState.gridSnapping) {
          finalX = snapToGrid(finalX, dragState.gridSize);
          finalY = snapToGrid(finalY, dragState.gridSize);
        }

        // Update drop zone indicator and show grid when dragging
        setDragState(prev => ({
          ...prev,
          showGrid: true, // Show grid when dragging
          isDragging: true, // Set dragging state
          showDropZone: true,
          dropZone: {
            x: finalX,
            y: finalY,
            width: componentWidth,
            height: componentHeight,
            type: (finalX >= 0 && finalY >= 0) ? 'valid' : 'invalid'
          },
          showPosition: true,
          position: {
            x: finalX,
            y: finalY,
            width: componentWidth,
            height: componentHeight
          }
        }));
      }
    },
    drop: (item: DragItem, monitor) => {
      const currentClientOffset = monitor.getClientOffset();
      const canvasRect = canvasDropTargetRef.current?.getBoundingClientRect();

      if (currentClientOffset && canvasRect) {
        const x = (currentClientOffset.x - canvasRect.left) / zoom;
        const y = (currentClientOffset.y - canvasRect.top) / zoom;

        if ('id' in item) {
          // Moving existing component
          const initialClientOffset = monitor.getInitialClientOffset();
          const initialSourceClientOffset = monitor.getInitialSourceClientOffset();

          if (initialClientOffset && initialSourceClientOffset) {
            const offsetX = initialClientOffset.x - initialSourceClientOffset.x;
            const offsetY = initialClientOffset.y - initialSourceClientOffset.y;

            const newComponentClientX = currentClientOffset.x - offsetX;
            const newComponentClientY = currentClientOffset.y - offsetY;

            const newX = Math.round((newComponentClientX - canvasRect.left) / zoom);
            const newY = Math.round((newComponentClientY - canvasRect.top) / zoom);

            handleMoveComponent(item.id, newX, newY);
          }
        } else if ('componentType' in item) {
          // Adding new component from toolbar
          let componentWidth = 120;
          let componentHeight = 40;

          // Use the same default sizes as in hover
          const defaultSizes: Record<string, { width: number; height: number }> = {
            'Button': { width: 120, height: 40 },
            'Text Input': { width: 200, height: 40 },
            'Text Area': { width: 300, height: 120 },
            'Navbar': { width: 800, height: 60 },
            'Sidebar': { width: 250, height: 400 },
            'Modal': { width: 400, height: 300 },
            'Table': { width: 500, height: 300 },
            'Desktop Browser': { width: 800, height: 600 },
            'Android Browser': { width: 375, height: 667 },
            'iOS Browser': { width: 375, height: 812 },
            'Image Placeholder': { width: 200, height: 150 },
            'Video Placeholder': { width: 300, height: 200 },

            // New Form Components
            'Menu Bar': { width: 300, height: 30 },
            'Multiline Button': { width: 120, height: 60 },
            'Number Stepper': { width: 100, height: 32 },
            'ON/OFF Switch': { width: 60, height: 30 },
            'Playback Controls': { width: 200, height: 40 },
            'Pointy Button': { width: 120, height: 40 },
            'Time Picker': { width: 120, height: 32 },
            'Rectangle': { width: 200, height: 100 },

            // New Layout Components
            'V Splitter': { width: 20, height: 300 },
            'V Tabs': { width: 200, height: 300 },
            'V Slider': { width: 30, height: 200 },
            'V Scroll Bar': { width: 16, height: 200 },
            'V Rule': { width: 20, height: 300 },
            'V Curly Bracket': { width: 30, height: 200 },
            'Window': { width: 400, height: 300 },

            // New Text Components
            'Text Title': { width: 300, height: 40 },
            'Text Subtitle': { width: 250, height: 30 },
            'Squiggly Line': { width: 200, height: 20 },

            // New Interactive Components
            'Red X': { width: 32, height: 32 },
            'Scratch-O': { width: 100, height: 30 },
            'Shape': { width: 100, height: 100 },
            'Site Map': { width: 300, height: 200 },
            'Smartphone': { width: 200, height: 400 },
            'Street Map': { width: 300, height: 200 },
            'Tab Bar': { width: 300, height: 50 },
            'Tag Cloud': { width: 300, height: 150 },
            'Tree Pane': { width: 250, height: 300 },
            'Video Player': { width: 400, height: 250 },
            'Volume Slider': { width: 150, height: 30 },
            'Webcam': { width: 200, height: 150 },

            // New Navigation Components
            'Toolbar': { width: 400, height: 40 },
            'Popover': { width: 200, height: 100 },
            'Progress Indicator': { width: 300, height: 30 }
          };

          const size = defaultSizes[item.componentType] || { width: 120, height: 40 };
          componentWidth = size.width;
          componentHeight = size.height;

          let finalX = x - componentWidth / 2;
          let finalY = y - componentHeight / 2;

          // Apply grid snapping
          if (dragState.gridSnapping) {
            finalX = snapToGrid(finalX, dragState.gridSize);
            finalY = snapToGrid(finalY, dragState.gridSize);
          }

          // Ensure component is within canvas bounds
          finalX = Math.max(0, finalX);
          finalY = Math.max(0, finalY);

          // Create new component
          const newComponent: WireframeComponent = {
            id: `${item.componentType.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
            type: item.componentType,
            x: finalX,
            y: finalY,
            width: componentWidth,
            height: componentHeight,
            text: getDefaultText(item.componentType),
            data: getDefaultData(item.componentType),
            zIndex: getNextZIndex(item.componentType)
          };

          // Add to current page
          if (activePageId) {
            console.log('Drag & Drop - Adding component:', {
              componentType: item.componentType,
              newComponent,
              activePageId,
              currentComponentsCount: (allPagesData[activePageId] || []).length
            });

            setAllPagesData(prev => ({
              ...prev,
              [activePageId]: [...(prev[activePageId] || []), newComponent]
            }));
          }
        }
      }

      // Hide drop zone indicators and grid when drag ends
      setDragState(prev => ({
        ...prev,
        showGrid: false, // Hide grid when drag ends
        isDragging: false, // Clear dragging state
        showDropZone: false,
        showPosition: false
      }));

      return undefined;
    },
  }), [handleMoveComponent, zoom, currentComponents, dragState.gridSnapping, dragState.gridSize]);

  const setRefs = useCallback((node: HTMLDivElement | null) => {
    canvasDropTargetRef.current = node;
    drop(node);
  }, [drop]);

  const handleSelectComponent = (component: WireframeComponent) => {
    setSelectedComponent(component);
    // Only bring to front if it's not a background layout component
    if (!backgroundLayoutComponents.includes(component.type)) {
      bringToFront(component.id);
    }
  };

  const handleUpdateComponent = (updatedComponent: WireframeComponent) => {
    if (!activePageId) return;

    // Find the original component for history tracking
    const originalComponent = (allPagesData[activePageId] || []).find(c => c.id === updatedComponent.id);
    if (!originalComponent) return;

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).map((component) =>
        component.id === updatedComponent.id ? updatedComponent : component
      ),
    }));
    setSelectedComponent(updatedComponent);

    // Add debounced history action for property updates
    addDebouncedAction(createAction(
      ACTION_TYPES.UPDATE_COMPONENT,
      `Update ${updatedComponent.type}`,
      () => {
        // Undo: restore original component
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).map((component) =>
            component.id === originalComponent.id ? originalComponent : component
          ),
        }));
        setSelectedComponent(originalComponent);
      },
      () => {
        // Redo: apply updated component
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).map((component) =>
            component.id === updatedComponent.id ? updatedComponent : component
          ),
        }));
        setSelectedComponent(updatedComponent);
      },
      { originalComponent, updatedComponent }
    ));
  };

  const handleDeleteComponent = (componentId: string) => {
    if (!activePageId) return;

    // Find the component to delete for history tracking
    const componentToDelete = (allPagesData[activePageId] || []).find(c => c.id === componentId);
    if (!componentToDelete) return;

    // Store the component's index for proper restoration
    const componentIndex = (allPagesData[activePageId] || []).findIndex(c => c.id === componentId);
    const wasSelected = selectedComponent?.id === componentId;

    setAllPagesData((prev) => ({
      ...prev,
      [activePageId]: (prev[activePageId] || []).filter((component) => component.id !== componentId),
    }));

    if (wasSelected) {
      setSelectedComponent(null);
    }

    // Add to history
    addAction(createAction(
      ACTION_TYPES.DELETE_COMPONENT,
      `Delete ${componentToDelete.type}`,
      () => {
        // Undo: restore the component at its original position
        setAllPagesData((prev) => {
          const currentComponents = prev[activePageId] || [];
          const newComponents = [...currentComponents];
          newComponents.splice(componentIndex, 0, componentToDelete);
          return {
            ...prev,
            [activePageId]: newComponents,
          };
        });
        if (wasSelected) {
          setSelectedComponent(componentToDelete);
        }
      },
      () => {
        // Redo: delete the component again
        setAllPagesData((prev) => ({
          ...prev,
          [activePageId]: (prev[activePageId] || []).filter((component) => component.id !== componentId),
        }));
        if (selectedComponent?.id === componentId) {
          setSelectedComponent(null);
        }
      },
      { component: componentToDelete, pageId: activePageId, index: componentIndex }
    ));
  };

  const handleEditData = (componentId: string) => {
    const component = currentComponents.find((comp) => comp.id === componentId);
    if (component) {
      setSelectedComponent(component);
      setIsDataModalOpen(true);
    }
  };

  const handleSaveData = (updatedComponent: WireframeComponent) => {
    handleUpdateComponent(updatedComponent);
  };

  const handleExportWireframe = () => {
    try {
      const wireframeData = {
        pages: pages.map(p => ({
          id: p.id,
          name: p.name,
          components: allPagesData[p.id] || []
        })),
        projectId: projectId,
        exportDate: new Date().toISOString(),
        version: "1.0"
      };

      console.log('Exporting wireframe data:', {
        pagesCount: wireframeData.pages.length,
        totalComponents: wireframeData.pages.reduce((sum, p) => sum + p.components.length, 0),
        activePageId: activePageId,
        currentComponents: currentComponents.length
      });

      const dataStr = JSON.stringify(wireframeData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `wireframe-${projectId || 'untitled'}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showSuccess('Wireframe exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      showError('Failed to export wireframe. Please try again.');
    }
  };

  const handleImportWireframe = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const importedData = JSON.parse(content);

          if (importedData.pages && Array.isArray(importedData.pages)) {
            // Clear existing selection
            setSelectedComponent(null);

            // Clear existing data and set new pages
            setPages(importedData.pages.map((p: any) => ({ id: p.id, name: p.name })));

            // Set page data
            const newAllPagesData: { [key: string]: WireframeComponent[] } = {};
            let totalComponents = 0;
            importedData.pages.forEach((page: any) => {
              newAllPagesData[page.id] = page.components || [];
              totalComponents += (page.components || []).length;
            });
            setAllPagesData(newAllPagesData);

            // Set current page to first imported page if available
            if (importedData.pages.length > 0) {
              setActivePageId(importedData.pages[0].id);
            }

            console.log('Import successful:', {
              pages: importedData.pages.length,
              totalComponents,
              firstPageId: importedData.pages[0]?.id,
              firstPageComponents: importedData.pages[0]?.components?.length || 0
            });

            showSuccess(`Successfully imported ${importedData.pages.length} wireframe pages with ${totalComponents} components!`);
          } else {
            showError('Invalid wireframe file format. Please select a valid wireframe JSON file.');
          }
        } catch (error) {
          console.error('Import error:', error);
          showError('Failed to import wireframe. Please check the file format.');
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };



  const toggleLeftPanel = () => setShowLeftPanel((prev) => !prev);
  const toggleRightPanel = () => setShowRightPanel((prev) => !prev);

  const handleWheel = (event: React.WheelEvent<HTMLDivElement>) => {
    event.preventDefault();
    const newZoom = Math.max(0.1, Math.min(3, zoom - event.deltaY * 0.001));
    setZoom(newZoom);
  };

  const toggleGrid = () => {
    setDragState(prev => ({ ...prev, showGrid: !prev.showGrid }));
  };

  const toggleGridSnapping = () => {
    setDragState(prev => ({ ...prev, gridSnapping: !prev.gridSnapping }));
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(3, prev + 0.1));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(0.1, prev - 0.1));
  };

  const handleZoomReset = () => {
    setZoom(1);
  };

  // Helper function to get default text for components
  const getDefaultText = (componentType: string): string => {
    const defaultTexts: Record<string, string> = {
      'Button': 'Button',
      'Text Input': 'Enter text...',
      'Text Area': 'Enter your message...',
      'Text Label': 'Label',
      'Text Paragraph': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      'Navbar': 'Brand',
      'Modal': 'Modal Title',
      'Search Box': 'Search...',
      'Date Picker': 'Select date...',
      'Dropdown': 'Select option...',
      'Radio Button': 'Radio Option',
      'Checkbox': 'Checkbox Option',
      'Desktop Browser': 'https://example.com',
      'Android Browser': 'Search or type URL',
      'iOS Browser': 'Search or enter website name',
      'Inspector Panel': 'Inspector',
      'Tooltip': 'Tooltip text',
      'Sticky Note': 'Note content',
      'Hotspot': 'Click here',
      'Image Placeholder': 'Image',
      'Video Placeholder': 'Video',
      'Map Placeholder': 'Map View',
      'Avatar': '👤',
      'Icon Placeholder': '⭐',
    };

    return defaultTexts[componentType] || componentType;
  };

  // Helper function to get default data for components
  const getDefaultData = (componentType: string): Record<string, any> => {
    const defaultData: Record<string, Record<string, any>> = {
      'Navbar': {
        navItems: ['Home', 'About', 'Services', 'Contact'],
        showAvatar: true,
        avatarText: '👤'
      },
      'Sidebar': {
        menuItems: ['Dashboard', 'Projects', 'Tasks', 'Reports', 'Settings']
      },
      'Dropdown': {
        options: ['Option 1', 'Option 2', 'Option 3']
      },
      'Table': {
        headers: ['Name', 'Email', 'Role'],
        rows: [
          ['John Doe', '<EMAIL>', 'Admin'],
          ['Jane Smith', '<EMAIL>', 'User']
        ]
      }
    };

    return defaultData[componentType] || {};
  };

  return (
    <WireframeErrorBoundary>
      <div className="h-screen flex flex-col">
        <SkipLink targetId="wireframe-canvas" />
        <AriaLiveRegion />

        <div className="p-4 border-b border-divider-lines bg-white flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Wireframe</h1>
          {projectId && (
            <p className="text-md text-creative-blue">
              Project: {decodeURIComponent(projectId)}
            </p>
          )}
          {/* Debug info */}
          <p className="text-sm text-gray-500">
            Active Page: {activePageId} | Components: {currentComponents.length}
          </p>
        </div>

        {/* Enhanced Controls - In Header */}
        <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-200">
          <button
            onClick={toggleGrid}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              dragState.showGrid
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
            title="Toggle Grid"
          >
            Grid
          </button>
          <button
            onClick={toggleGridSnapping}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              dragState.gridSnapping
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
            title="Toggle Grid Snapping"
          >
            Snap
          </button>
          <div className="flex items-center gap-1">
            {/* Zoom Presets */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => setZoom(0.25)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  Math.round(zoom * 100) === 25
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
                title="25%"
              >
                25%
              </button>
              <button
                onClick={() => setZoom(0.5)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  Math.round(zoom * 100) === 50
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
                title="50%"
              >
                50%
              </button>
              <button
                onClick={() => setZoom(1)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  Math.round(zoom * 100) === 100
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
                title="100%"
              >
                100%
              </button>
              <button
                onClick={() => setZoom(1.5)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  Math.round(zoom * 100) === 150
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
                title="150%"
              >
                150%
              </button>
            </div>

            {/* Manual Zoom Control */}
            <div className="flex items-center gap-1 px-2 py-1 bg-white border border-gray-300 rounded">
              <button
                onClick={() => setZoom(Math.max(0, zoom - 0.1))}
                className="text-xs text-gray-600 hover:text-gray-800 px-1"
                title="Zoom Out"
              >
                −
              </button>
              <input
                type="number"
                value={Math.round(zoom * 100)}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  if (inputValue === '') {
                    setZoom(1); // Default to 100% if empty
                  } else {
                    const newZoom = Math.max(0.1, Math.min(500, parseInt(inputValue) || 100)) / 100;
                    setZoom(newZoom);
                  }
                }}
                className="w-12 text-xs text-center bg-transparent border-none outline-none"
                min="0"
                max="500"
                placeholder="0"
              />
              <span className="text-xs text-gray-600">%</span>
              <button
                onClick={() => setZoom(Math.min(5, zoom + 0.1))}
                className="text-xs text-gray-600 hover:text-gray-800 px-1"
                title="Zoom In"
              >
                +
              </button>
            </div>
          </div>
          <button
            onClick={() => setShowShortcutsHelp(true)}
            className="px-3 py-1 text-xs bg-white border border-gray-300 rounded text-gray-600 hover:bg-gray-50"
            title="Keyboard Shortcuts (Press ?)"
          >
            ?
          </button>
          <button
            onClick={() => setShowHelpDocumentation(true)}
            className="px-3 py-1 text-xs bg-white border border-gray-300 rounded text-gray-600 hover:bg-gray-50"
            title="Help & Documentation"
          >
            📖
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <Button className="secondary-button" onClick={handleImportWireframe}>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button className="primary-button" onClick={handleExportWireframe}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>

          {/* Debug button to test component addition */}
          <Button
            onClick={() => {
              console.log('Debug: Adding test button');
              handleAddComponent('Button');
            }}
            variant="outline"
            size="sm"
            style={{ backgroundColor: '#ff0000', color: 'white' }}
          >
            DEBUG: Add Button
          </Button>

          {/* Debug button to directly add component to state */}
          <Button
            onClick={() => {
              console.log('Debug: Directly adding component to state');
              const effectiveProjectId = projectId || 'default-project';
              const defaultPageId = `page-${Date.now()}`;

              // Ensure we have a page
              if (!activePageId) {
                const defaultPage: WireframePageItem = { id: defaultPageId, name: "Debug Page" };
                setPages([defaultPage]);
                setActivePageId(defaultPageId);
              }

              const testComponent: WireframeComponent = {
                id: `debug-${Date.now()}`,
                type: 'Button',
                x: 50,
                y: 50,
                width: 100,
                height: 40,
                text: 'Debug Button',
                zIndex: 1
              };

              const pageId = activePageId || defaultPageId;
              setAllPagesData(prev => ({
                ...prev,
                [pageId]: [...(prev[pageId] || []), testComponent]
              }));

              console.log('Debug component added:', testComponent);
            }}
            variant="outline"
            size="sm"
            style={{ backgroundColor: '#00ff00', color: 'black' }}
          >
            DEBUG: Direct Add
          </Button>
          <Button className="secondary-button" onClick={toggleLeftPanel}>
            {showLeftPanel ? <ChevronLeft className="mr-2 h-4 w-4" /> : <ChevronDown className="mr-2 h-4 w-4" />}
            {showLeftPanel ? "Hide Pages" : "Show Pages"}
          </Button>
          <Button className="secondary-button" onClick={toggleRightPanel}>
            {showRightPanel ? <ChevronRight className="mr-2 h-4 w-4" /> : <ChevronDown className="mr-2 h-4 w-4" />}
            {showRightPanel ? "Hide Props" : "Show Props"}
          </Button>
        </div>
      </div>
      <div className="flex flex-1 overflow-hidden relative">
        {showLeftPanel && (
          <div className="w-64 z-10 border-r border-divider-lines">
            <WireframePagesPanel
              pages={pages}
              activePageId={activePageId}
              onAddPage={handleAddPage}
              onDeletePage={handleDeletePage}
              onSelectPage={handleSelectPage}
              onUpdatePageName={handleUpdatePageName}
              onReorderPages={handleReorderPages}
            />
          </div>
        )}
        <div className="flex-1 flex flex-col bg-white overflow-hidden">
          <WireframeToolbar onAddComponent={handleAddComponent} />
          <div
            id="wireframe-canvas"
            ref={canvasRef}
            className="flex-1 bg-canvas-background p-4 relative overflow-auto"
            onWheel={handleWheel}
            role="application"
            aria-label="Wireframe design canvas"
          >
            <div
              ref={setRefs}
              className="w-full h-full relative"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: "top left",
                border: "1px solid #ccc",
                minWidth: '2000px',
                minHeight: '2000px'
              }}
              onClick={(e) => {
                // Deselect component when clicking on empty canvas
                if (e.target === e.currentTarget) {
                  setSelectedComponent(null);
                }
              }}
            >
              {/* Grid Overlay */}
              <GridOverlay
                gridSize={dragState.gridSize}
                visible={dragState.showGrid}
                zoom={zoom}
                canvasWidth={2000}
                canvasHeight={2000}
                isDragging={dragState.isDragging}
              />

              {/* Components */}
              {activePageId && currentComponents.length > 0 ? (
                (() => {
                  console.log('Rendering components:', {
                    activePageId,
                    currentComponentsCount: currentComponents.length,
                    components: currentComponents.map(c => ({ id: c.id, type: c.type, x: c.x, y: c.y }))
                  });
                  return currentComponents
                    .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0)) // Sort by z-index
                    .map((component) => (
                    <ResizableComponent
                      key={component.id}
                      component={component}
                      isSelected={selectedComponent?.id === component.id}
                      onSelect={handleSelectComponent}
                      onDoubleClick={() => handleEditData(component.id)}
                      onResize={handleResizeComponent}
                      onMove={handleMoveComponent}
                      onUpdate={handleUpdateComponent}
                      onDragStart={() => setDragState(prev => ({ ...prev, showGrid: true, isDragging: true }))}
                      onDragEnd={() => setDragState(prev => ({ ...prev, showGrid: false, isDragging: false }))}
                      gridSnapping={dragState.gridSnapping}
                      gridSize={dragState.gridSize}
                      zIndex={component.zIndex || 1}
                    />
                  ));
                })()
              ) : (
                <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-lg">
                  {activePageId ? "Drag components here or select a page." : "Select or create a page to start designing."}
                </div>
              )}

              {/* Drop Zone Indicator */}
              <DropZoneIndicator
                x={dragState.dropZone.x}
                y={dragState.dropZone.y}
                width={dragState.dropZone.width}
                height={dragState.dropZone.height}
                visible={dragState.showDropZone}
                type={dragState.dropZone.type}
                zoom={zoom}
              />

              {/* Position Indicator */}
              <PositionIndicator
                x={dragState.position.x}
                y={dragState.position.y}
                width={dragState.position.width}
                height={dragState.position.height}
                visible={dragState.showPosition}
                canvasWidth={2000}
                canvasHeight={2000}
                zoom={zoom}
              />
            </div>


          </div>
        </div>
        {showRightPanel && (
          <div className="w-64 z-10 border-l border-divider-lines">
            <WireframePropertiesPanel
              selectedComponent={selectedComponent}
              onUpdateComponent={handleUpdateComponent}
              onDeleteComponent={handleDeleteComponent}
              onEditData={() => selectedComponent && handleEditData(selectedComponent.id)}
              onBringToFront={bringToFront}
              onSendToBack={sendToBack}
              onBringForward={bringForward}
              onSendBackward={sendBackward}
            />
          </div>
        )}
      </div>
      <ComponentDataModal
        isOpen={isDataModalOpen}
        onOpenChange={setIsDataModalOpen}
        component={selectedComponent}
        onSave={handleSaveData}
      />

      {/* Keyboard Shortcuts */}
      <KeyboardShortcuts
        onToggleGrid={toggleGrid}
        onToggleSnapping={toggleGridSnapping}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onZoomReset={handleZoomReset}
        onDelete={() => selectedComponent && handleDeleteComponent(selectedComponent.id)}
      />

      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp
        visible={showShortcutsHelp}
        onClose={() => setShowShortcutsHelp(false)}
      />

      {/* Help Documentation */}
      <HelpDocumentation
        visible={showHelpDocumentation}
        onClose={() => setShowHelpDocumentation(false)}
      />

      {/* Accessibility Enhancements */}
      <AccessibilityEnhancements
        components={currentComponents}
        selectedComponent={selectedComponent}
        onSelectComponent={handleSelectComponent}
        onMoveComponent={handleMoveComponent}
        gridSize={dragState.gridSize}
      />
      </div>
    </WireframeErrorBoundary>
  );
};

// Main wrapper component with HistoryProvider
const WireframePage = () => {
  return (
    <HistoryProvider options={{ maxHistorySize: 100, debounceMs: 300 }}>
      <WireframePageContent />
    </HistoryProvider>
  );
};

export default WireframePage;