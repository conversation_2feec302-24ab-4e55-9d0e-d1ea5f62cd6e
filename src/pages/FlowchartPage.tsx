import React, { useState, useRef, useCallback, useEffect } from "react";
import { useParams } from "react-router-dom";
import { FlowchartToolbar } from "@/components/flowchart/FlowchartToolbar";
import { FlowchartSettingsPanel } from "@/components/flowchart/FlowchartSettingsPanel";
import { FlowchartElementDisplay, ConnectionNodeType, SnappedNodeInfo, ResizeHandleType, ExtensionPointType } from "@/components/flowchart/FlowchartElementDisplay";
import { Button } from "@/components/ui/button";
import { Download, Upload, ArrowRight } from "lucide-react";
import { showSuccess, showError } from "@/utils/toast";
import { importFlowchartToWireframe, FlowchartImportData, WireframeImportData } from "@/utils/importUtils";
import { v4 as uuidv4 } from 'uuid';
import { HistoryProvider, useHistoryContext, useHistoryKeyboardShortcuts } from "@/contexts/HistoryContext";
import { createAction, ACTION_TYPES } from "@/hooks/useHistory";

export interface Point {
  x: number;
  y: number;
}

export interface FlowchartElement {
  id: string;
  type: 'text' | 'box' | 'diamond' | 'line' | 'arrow' | 'pencil';
  x: number;
  y: number;
  width?: number;
  height?: number;
  text?: string;
  fontSize?: number;
  textColor?: string;
  fillColor?: string;
  borderColor?: string;
  points?: Point[];
  strokeColor?: string;
  strokeWidth?: number;
  rotation?: number;
  sourceElementId?: string;
  sourceNode?: ConnectionNodeType;
  targetElementId?: string;
  targetNode?: ConnectionNodeType;
}

const MIN_LINE_LENGTH = 5;
const SNAP_DISTANCE = 15;
const MIN_BOX_SIZE = 20;
const MIN_TEXT_WIDTH = 30;
const MIN_TEXT_HEIGHT = 16;
const DEFAULT_NEW_BOX_WIDTH = 120;
const DEFAULT_NEW_BOX_HEIGHT = 60;
const DEFAULT_TEXT_WIDTH = 100;
const DEFAULT_TEXT_HEIGHT = 30;
const MIN_PENCIL_PATH_LENGTH = 2;

const FlowchartPageContent = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { addAction, addDebouncedAction } = useHistoryContext();

  // Enable keyboard shortcuts for undo/redo
  useHistoryKeyboardShortcuts();

  // State declarations first to avoid initialization order issues
  const [activeTool, setActiveTool] = useState<string>('pointer');
  const [elements, setElements] = useState<FlowchartElement[]>([]);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);

  // Helper function to add elements with history tracking
  const addElementsWithHistory = useCallback((newElements: FlowchartElement[], description: string) => {
    const previousElements = [...elements];
    setElements(prev => [...prev, ...newElements]);

    // Add to history
    addAction(createAction(
      ACTION_TYPES.ADD_ELEMENT,
      description,
      () => {
        // Undo: remove the added elements
        setElements(previousElements);
        if (newElements.some(el => el.id === selectedElementId)) {
          setSelectedElementId(null);
        }
      },
      () => {
        // Redo: add the elements back
        setElements(prev => [...prev, ...newElements]);
      },
      { addedElements: newElements }
    ));
  }, [elements, selectedElementId, addAction]);

  const selectedElement = elements.find(el => el.id === selectedElementId) || null;

  const [canvasPan, setCanvasPan] = useState({ x: 0, y: 0 });
  const [canvasZoom, setCanvasZoom] = useState(1);

  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingStartPoint, setDrawingStartPoint] = useState<Point | null>(null);
  const [tempElement, setTempElement] = useState<FlowchartElement | null>(null);

  const [isDraggingElement, setIsDraggingElement] = useState(false);
  const [dragStartOffset, setDragStartOffset] = useState<Point | null>(null);
  const [originalElementState, setOriginalElementState] = useState<FlowchartElement | null>(null);

  const [isPanning, setIsPanning] = useState(false);
  const [initialPanPosition, setInitialPanPosition] = useState<Point>({ x: 0, y: 0 });

  const [isEditingText, setIsEditingText] = useState(false);
  const [editingTextValue, setEditingTextValue] = useState("");

  // Double-click detection state
  const [lastClickTime, setLastClickTime] = useState(0);
  const [lastClickedElementId, setLastClickedElementId] = useState<string | null>(null);
  const [isEnteringEditMode, setIsEnteringEditMode] = useState(false);

  const [drawingFromNodeInfo, setDrawingFromNodeInfo] = useState<{ elementId: string, nodeType: ConnectionNodeType, position: Point } | null>(null);
  const [snappedToNodeInfo, setSnappedToNodeInfo] = useState<SnappedNodeInfo | null>(null);

  const [isResizing, setIsResizing] = useState(false);
  const [resizingElementInfo, setResizingElementInfo] = useState<{
    id: string,
    handleType: ResizeHandleType,
    originalElement: FlowchartElement,
    originalMousePos: Point
  } | null>(null);

  const [isResizingLineEndPoint, setIsResizingLineEndPoint] = useState<{
    elementId: string;
    pointIndex: 0 | 1;
    originalElement: FlowchartElement;
  } | null>(null);

  // Hover states for FigJam-like extension behavior
  const [hoveredElementId, setHoveredElementId] = useState<string | null>(null);
  const [hoveredExtensionPoint, setHoveredExtensionPoint] = useState<{
    elementId: string;
    extensionType: ExtensionPointType;
    position: Point;
  } | null>(null);

  const canvasRef = useRef<HTMLDivElement>(null);

  // Check for imported wireframe elements on component mount
  useEffect(() => {
    const importedElementsString = localStorage.getItem('importedFlowchartElements');
    if (importedElementsString) {
      try {
        const importedElements: FlowchartElement[] = JSON.parse(importedElementsString);

        if (importedElements.length > 0) {
          // Add imported elements to existing elements
          setElements(prevElements => [...prevElements, ...importedElements]);

          // Clear the imported data from localStorage
          localStorage.removeItem('importedFlowchartElements');

          console.log('Successfully loaded imported wireframe elements:', importedElements);
        }
      } catch (error) {
        console.error('Error parsing imported flowchart elements:', error);
        localStorage.removeItem('importedFlowchartElements');
      }
    }
  }, []); // Run only once on mount

  const getMousePosition = useCallback((event: React.MouseEvent | MouseEvent): Point => {
    if (!canvasRef.current) return { x: 0, y: 0 };
    const rect = canvasRef.current.getBoundingClientRect();

    // Raw mouse position
    const rawX = event.clientX - rect.left;
    const rawY = event.clientY - rect.top;

    // Try the correct transformation for CSS transform: translate(pan) scale(zoom)
    const transformedX = (rawX - canvasPan.x) / canvasZoom;
    const transformedY = (rawY - canvasPan.y) / canvasZoom;



    return {
      x: transformedX,
      y: transformedY,
    };
  }, [canvasPan, canvasZoom]);

  const getNodePositions = useCallback((element: FlowchartElement): Partial<Record<ConnectionNodeType, Point>> => {
    if ((element.type !== 'box' && element.type !== 'diamond') || typeof element.width === 'undefined' || typeof element.height === 'undefined') return {};
    return {
      top: { x: element.x + element.width / 2, y: element.y },
      bottom: { x: element.x + element.width / 2, y: element.y + element.height },
      left: { x: element.x, y: element.y + element.height / 2 },
      right: { x: element.x + element.width, y: element.y + element.height / 2 },
    };
  }, []);

  const updateConnectedLines = useCallback((updatedBox: FlowchartElement, currentElements: FlowchartElement[]): FlowchartElement[] => {
    return currentElements.map(el => {
      if ((el.type === 'line' || el.type === 'arrow') && el.points && el.points.length === 2) {
        let newPoints = [...el.points] as [Point, Point];
        let changed = false;

        if (el.sourceElementId === updatedBox.id && el.sourceNode) {
          const newSourcePos = getNodePositions(updatedBox)[el.sourceNode];
          if (newSourcePos) {
            newPoints[0] = newSourcePos;
            changed = true;
          }
        }
        if (el.targetElementId === updatedBox.id && el.targetNode) {
          const newTargetPos = getNodePositions(updatedBox)[el.targetNode];
          if (newTargetPos) {
            newPoints[1] = newTargetPos;
            changed = true;
          }
        }
        return changed ? { ...el, points: newPoints } : el;
      }
      return el;
    });
  }, [getNodePositions]);


  // Temporarily commented out to debug double-click issue
  // useEffect(() => {
  //   if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'box') && isEditingText) {
  //       setEditingTextValue(selectedElement.text || "");
  //   }
  // }, [selectedElement, isEditingText]);

  const handleUpdateElementProperty = (propertyName: keyof FlowchartElement, value: any) => {
    if (!selectedElementId) return;

    // Find the original element for history tracking
    const originalElement = elements.find(el => el.id === selectedElementId);
    if (!originalElement) return;

    const originalValue = originalElement[propertyName];

    // Only update if value actually changed
    if (originalValue === value) return;

    setElements(prevElements => {
        let newElements = prevElements.map(el =>
            el.id === selectedElementId ? { ...el, [propertyName]: value } : el
        );
        if ((selectedElement?.type === 'box' || selectedElement?.type === 'diamond') && (propertyName === 'x' || propertyName === 'y' || propertyName === 'width' || propertyName === 'height')) {
            const updatedBox = newElements.find(el => el.id === selectedElementId);
            if (updatedBox) {
                newElements = updateConnectedLines(updatedBox, newElements);
            }
        }
        return newElements;
    });

    // Add debounced history action for property updates
    addDebouncedAction(createAction(
      ACTION_TYPES.UPDATE_ELEMENT,
      `Update ${originalElement.type} ${propertyName}`,
      () => {
        // Undo: restore original value
        setElements(prevElements => {
          let newElements = prevElements.map(el =>
              el.id === selectedElementId ? { ...el, [propertyName]: originalValue } : el
          );
          if ((originalElement.type === 'box' || originalElement.type === 'diamond') && (propertyName === 'x' || propertyName === 'y' || propertyName === 'width' || propertyName === 'height')) {
              const restoredBox = newElements.find(el => el.id === selectedElementId);
              if (restoredBox) {
                  newElements = updateConnectedLines(restoredBox, newElements);
              }
          }
          return newElements;
        });
      },
      () => {
        // Redo: apply new value
        setElements(prevElements => {
          let newElements = prevElements.map(el =>
              el.id === selectedElementId ? { ...el, [propertyName]: value } : el
          );
          if ((originalElement.type === 'box' || originalElement.type === 'diamond') && (propertyName === 'x' || propertyName === 'y' || propertyName === 'width' || propertyName === 'height')) {
              const updatedBox = newElements.find(el => el.id === selectedElementId);
              if (updatedBox) {
                  newElements = updateConnectedLines(updatedBox, newElements);
              }
          }
          return newElements;
        });
      },
      { elementId: selectedElementId, propertyName, originalValue, newValue: value }
    ));
  };

  const handleDeleteSelectedElement = useCallback(() => {
    if (!selectedElementId) return;
    const elementToDelete = elements.find(el => el.id === selectedElementId);
    if (!elementToDelete) return;

    // Store connected elements that will be deleted for history tracking
    let connectedElements: FlowchartElement[] = [];
    if (elementToDelete.type === 'box' || elementToDelete.type === 'diamond') {
      connectedElements = elements.filter(el =>
        (el.type === 'line' || el.type === 'arrow') &&
        (el.sourceElementId === selectedElementId || el.targetElementId === selectedElementId)
      );
    }

    // Store the element's index for proper restoration
    const elementIndex = elements.findIndex(el => el.id === selectedElementId);
    const allDeletedElements = [elementToDelete, ...connectedElements];

    setElements(prevElements => {
      let newElements = prevElements.filter(el => el.id !== selectedElementId);
      if (elementToDelete.type === 'box' || elementToDelete.type === 'diamond') {
        newElements = newElements.filter(el =>
          (el.type !== 'line' && el.type !== 'arrow') ||
          (el.sourceElementId !== selectedElementId && el.targetElementId !== selectedElementId)
        );
      }
      return newElements;
    });
    setSelectedElementId(null);

    // Add to history
    addAction(createAction(
      ACTION_TYPES.DELETE_ELEMENT,
      `Delete ${elementToDelete.type}`,
      () => {
        // Undo: restore all deleted elements
        setElements(prevElements => {
          const newElements = [...prevElements];
          // Insert main element at its original position
          newElements.splice(elementIndex, 0, elementToDelete);
          // Add connected elements back
          connectedElements.forEach(connectedEl => {
            newElements.push(connectedEl);
          });
          return newElements;
        });
        setSelectedElementId(selectedElementId);
      },
      () => {
        // Redo: delete the elements again
        setElements(prevElements => {
          let newElements = prevElements.filter(el => el.id !== selectedElementId);
          if (elementToDelete.type === 'box' || elementToDelete.type === 'diamond') {
            newElements = newElements.filter(el =>
              (el.type !== 'line' && el.type !== 'arrow') ||
              (el.sourceElementId !== selectedElementId && el.targetElementId !== selectedElementId)
            );
          }
          return newElements;
        });
        setSelectedElementId(null);
      },
      { deletedElements: allDeletedElements, elementIndex }
    ));
  }, [selectedElementId, elements, addAction]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Handle Escape key to exit text editing or deselect
      if (event.key === 'Escape') {
        if (isEditingText) {
          event.preventDefault();
          finalizeTextEdit();
        } else if (selectedElementId) {
          event.preventDefault();
          setSelectedElementId(null);
          setIsDraggingElement(false);
          setDragStartOffset(null);
          setOriginalElementState(null);
        }
        return;
      }

      if (selectedElementId && document.activeElement?.tagName !== 'INPUT' && document.activeElement?.tagName !== 'TEXTAREA') {
        if (event.key === 'Delete' || event.key === 'Backspace') {
          event.preventDefault();
          handleDeleteSelectedElement();
        } else if (event.key === 'Enter' || event.key === 'F2') {
          // Enter or F2 to edit text
          const selectedElement = elements.find(el => el.id === selectedElementId);
          if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'box' || selectedElement.type === 'diamond')) {
            event.preventDefault();
            setEditingTextValue(selectedElement.text || "");
            setIsEditingText(true);

          }
        }
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedElementId, handleDeleteSelectedElement, elements, isEditingText]);


  const handleNodeMouseDown = (
    e: React.MouseEvent, sourceElementId: string, nodeType: ConnectionNodeType, nodePosition: Point
  ) => {
    setIsDrawing(true); setDrawingStartPoint(nodePosition);
    setDrawingFromNodeInfo({ elementId: sourceElementId, nodeType, position: nodePosition });
    const newId = uuidv4();
    // Default to arrow when drawing from node, can be changed by activeTool later if needed
    setTempElement({
      id: newId, type: 'arrow', x:0, y:0,
      points: [nodePosition, nodePosition], strokeColor: '#000000', strokeWidth: 2,
      sourceElementId: sourceElementId, sourceNode: nodeType,
    });
  };

  const handleResizeHandleMouseDown = (
    e: React.MouseEvent, elementId: string, handleType: ResizeHandleType
  ) => {
    const elementToResize = elements.find(el => el.id === elementId);
    if (!elementToResize || (elementToResize.type !== 'box' && elementToResize.type !== 'text' && elementToResize.type !== 'diamond')) return;

    e.stopPropagation();
    setSelectedElementId(elementId);
    setIsResizing(true);
    setIsDraggingElement(false);
    const originalMousePos = getMousePosition(e);
    setResizingElementInfo({
      id: elementId,
      handleType,
      originalElement: { ...elementToResize },
      originalMousePos,
    });
  };

  const handleLineEndPointMouseDown = (
    e: React.MouseEvent,
    elementId: string,
    pointIndex: 0 | 1
  ) => {
    const lineElement = elements.find(el => el.id === elementId && (el.type === 'line' || el.type === 'arrow'));
    if (!lineElement || !lineElement.points || lineElement.points.length !== 2) return;

    e.stopPropagation();
    setSelectedElementId(elementId);
    setIsResizingLineEndPoint({
      elementId,
      pointIndex,
      originalElement: { ...lineElement },
    });
    setIsDraggingElement(false);
  };

  const handleExtensionPointClick = (
    e: React.MouseEvent,
    sourceElementId: string,
    extensionType: ExtensionPointType,
    extensionPosition: Point
  ) => {
    e.stopPropagation();

    // Create a new box at the extension position
    const newBoxId = uuidv4();
    const newBox: FlowchartElement = {
      id: newBoxId,
      type: 'box',
      x: extensionPosition.x - DEFAULT_NEW_BOX_WIDTH / 2,
      y: extensionPosition.y - DEFAULT_NEW_BOX_HEIGHT / 2,
      width: DEFAULT_NEW_BOX_WIDTH,
      height: DEFAULT_NEW_BOX_HEIGHT,
      text: 'New Box',
      fillColor: '#FFFFFF',
      borderColor: '#000000',
      fontSize: 16,
      textColor: '#000000',
    };

    // Create an arrow connecting the source box to the new box
    const sourceElement = elements.find(el => el.id === sourceElementId);
    if (sourceElement && (sourceElement.type === 'box' || sourceElement.type === 'diamond')) {
      const sourceNodePositions = getNodePositions(sourceElement);
      const newBoxNodePositions = getNodePositions(newBox);

      // Determine the opposite side for the target connection
      const targetExtensionType: ExtensionPointType =
        extensionType === 'top' ? 'bottom' :
        extensionType === 'bottom' ? 'top' :
        extensionType === 'left' ? 'right' : 'left';

      const sourcePosition = sourceNodePositions[extensionType];
      const targetPosition = newBoxNodePositions[targetExtensionType];

      if (sourcePosition && targetPosition) {
        const arrowId = uuidv4();
        const arrow: FlowchartElement = {
          id: arrowId,
          type: 'arrow',
          x: 0,
          y: 0,
          points: [sourcePosition, targetPosition],
          strokeColor: '#000000',
          strokeWidth: 2,
          sourceElementId: sourceElementId,
          sourceNode: extensionType,
          targetElementId: newBoxId,
          targetNode: targetExtensionType,
        };

        // Add both the new box and the arrow
        addElementsWithHistory([newBox, arrow], `Add connected box`);
        setSelectedElementId(newBoxId);
      } else {
        // Fallback: just add the box without the arrow
        addElementsWithHistory([newBox], `Add box`);
        setSelectedElementId(newBoxId);
      }
    }
  };

  // Hover handlers for FigJam-like extension behavior
  const handleElementMouseEnter = (elementId: string) => {
    if (activeTool === 'pointer' && !isDrawing && !isDraggingElement && !isResizing) {
      setHoveredElementId(elementId);
    }
  };

  const handleElementMouseLeave = () => {
    setHoveredElementId(null);
    setHoveredExtensionPoint(null);
  };

  const handleExtensionPointMouseEnter = (
    elementId: string,
    extensionType: ExtensionPointType,
    position: Point
  ) => {
    if (activeTool === 'pointer' && !isDrawing && !isDraggingElement && !isResizing) {
      setHoveredExtensionPoint({ elementId, extensionType, position });
    }
  };

  const handleExtensionPointMouseLeave = () => {
    setHoveredExtensionPoint(null);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    console.log('Canvas handleMouseDown called', { isEnteringEditMode, isEditingText });

    if (isEnteringEditMode) {
      console.log('Canvas handleMouseDown: ignoring because isEnteringEditMode is true');
      return;
    }

    if (activeTool === 'hand') {
      setIsPanning(true);
      // Store the initial mouse position in screen coordinates
      setDrawingStartPoint({ x: e.clientX, y: e.clientY });
      // Store the initial pan position
      setInitialPanPosition({ x: canvasPan.x, y: canvasPan.y });
      return;
    }

    const pos = getMousePosition(e);

    const target = e.target as HTMLElement;

    // Handle text editing blur
    if (isEditingText) {
        console.log('handleMouseDown: isEditingText is true, checking target:', target);
        if (!target.closest('[data-component-id]') && !target.closest('.w-64.bg-panel-background')) {
          console.log('handleMouseDown: calling finalizeTextEdit because clicked outside element');
          finalizeTextEdit();
        }
        if (target.closest('textarea[autoFocus]') || target.closest('.w-64.bg-panel-background')) {
            console.log('handleMouseDown: returning early because clicked on textarea or panel');
            return;
        }
    }

    // Deselect element when clicking on empty canvas or outside elements
    const clickedOnElement = target.closest('[data-component-id]');
    const clickedOnPanel = target.closest('.w-64.bg-panel-background'); // Settings panel
    const clickedOnToolbar = target.closest('.w-16.bg-side-toolbar'); // Toolbar

    if (!clickedOnElement && !clickedOnPanel && !clickedOnToolbar && !isResizing && !isResizingLineEndPoint && !isDrawing) {
        // Clicked on empty space - deselect everything
        setSelectedElementId(null);
        setIsDraggingElement(false);
        setDragStartOffset(null);
        setOriginalElementState(null);
        if (isEditingText) finalizeTextEdit();
    }

    if (activeTool === 'box' && !drawingFromNodeInfo) {
      setIsDrawing(true); setDrawingStartPoint(pos); const newId = uuidv4();
      setTempElement({
        id: newId, type: 'box', x: pos.x, y: pos.y, width: 0, height: 0,
        text: 'Box', fillColor: '#FFFFFF', borderColor: '#000000',
      });
    } else if (activeTool === 'diamond' && !drawingFromNodeInfo) {
      setIsDrawing(true); setDrawingStartPoint(pos); const newId = uuidv4();
      setTempElement({
        id: newId, type: 'diamond', x: pos.x, y: pos.y, width: 0, height: 0,
        text: 'Decision', fillColor: '#FFFFFF', borderColor: '#000000',
      });
    } else if (activeTool === 'text' && !drawingFromNodeInfo) {
      setDrawingStartPoint(pos);
    } else if ((activeTool === 'line' || activeTool === 'arrow' || activeTool === 'horizontal-line' || activeTool === 'vertical-line') && !drawingFromNodeInfo) {
      setIsDrawing(true); setDrawingStartPoint(pos); const newId = uuidv4();
      // Use 'line' type for constrained lines, arrow type if 'arrow' tool is selected
      const lineType = activeTool === 'arrow' ? 'arrow' : 'line';
      setTempElement({
        id: newId, type: lineType, x:0, y:0,
        points: [pos, pos], strokeColor: '#000000', strokeWidth: 2,
      });
    } else if (activeTool === 'pencil' && !drawingFromNodeInfo) {
      setIsDrawing(true); setDrawingStartPoint(pos); const newId = uuidv4();
      setTempElement({
        id: newId, type: 'pencil', x: 0, y: 0, width: 1, height: 1,
        points: [pos], strokeColor: '#000000', strokeWidth: 2,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const currentMousePos = getMousePosition(e);
    let newSnappedToNodeInfo: SnappedNodeInfo | null = null;

    if (isPanning && activeTool === 'hand' && drawingStartPoint) {
      // Calculate the delta from the initial mouse position
      const deltaX = e.clientX - drawingStartPoint.x;
      const deltaY = e.clientY - drawingStartPoint.y;

      // Update the pan by adding the delta to the initial pan position
      setCanvasPan({
        x: initialPanPosition.x + deltaX,
        y: initialPanPosition.y + deltaY
      });
      return;
    }

    if (isResizingLineEndPoint) {
      const { elementId, pointIndex, originalElement } = isResizingLineEndPoint;
      if (!originalElement.points || originalElement.points.length !== 2) return;
      const newPoints = [...originalElement.points] as [Point, Point];
      let potentialSnapPoint = currentMousePos;

      for (const el of elements) {
        if ((el.type === 'box' || el.type === 'diamond') && el.id !== originalElement.sourceElementId && el.id !== originalElement.targetElementId) {
          const targetNodePositions = getNodePositions(el);
          for (const [nodeType, nodePos] of Object.entries(targetNodePositions)) {
            if (nodePos) {
              const dist = Math.sqrt(Math.pow(currentMousePos.x - nodePos.x, 2) + Math.pow(currentMousePos.y - nodePos.y, 2));
              if (dist < SNAP_DISTANCE / canvasZoom) {
                potentialSnapPoint = nodePos;
                newSnappedToNodeInfo = { elementId: el.id, nodeType: nodeType as ConnectionNodeType };
                break;
              }
            }
          }
        }
        if (newSnappedToNodeInfo) break;
      }

      newPoints[pointIndex] = potentialSnapPoint;

      setElements(prevElements =>
        prevElements.map(el =>
          el.id === elementId ? { ...el, points: newPoints } : el
        )
      );
      setSnappedToNodeInfo(newSnappedToNodeInfo);
      return;
    }


    if (isResizing && resizingElementInfo) {
      const { id, handleType, originalElement, originalMousePos } = resizingElementInfo;
      let { x, y, width = 0, height = 0 } = originalElement;
      const dx = currentMousePos.x - originalMousePos.x;
      const dy = currentMousePos.y - originalMousePos.y;

      // Determine minimum sizes based on element type
      const minWidth = originalElement.type === 'text' ? MIN_TEXT_WIDTH : MIN_BOX_SIZE;
      const minHeight = originalElement.type === 'text' ? MIN_TEXT_HEIGHT : MIN_BOX_SIZE;

      switch (handleType) {
        case 'e': width = Math.max(minWidth, originalElement.width! + dx); break;
        case 'se':
          width = Math.max(minWidth, originalElement.width! + dx);
          height = Math.max(minHeight, originalElement.height! + dy);
          break;
        case 's': height = Math.max(minHeight, originalElement.height! + dy); break;
        case 'sw':
          width = Math.max(minWidth, originalElement.width! - dx);
          height = Math.max(minHeight, originalElement.height! + dy);
          x = originalElement.x + dx;
          break;
        case 'w':
          width = Math.max(minWidth, originalElement.width! - dx);
          x = originalElement.x + dx;
          break;
        case 'nw':
          width = Math.max(minWidth, originalElement.width! - dx);
          height = Math.max(minHeight, originalElement.height! - dy);
          x = originalElement.x + dx;
          y = originalElement.y + dy;
          break;
        case 'n':
          height = Math.max(minHeight, originalElement.height! - dy);
          y = originalElement.y + dy;
          break;
        case 'ne':
          width = Math.max(minWidth, originalElement.width! + dx);
          height = Math.max(minHeight, originalElement.height! - dy);
          y = originalElement.y + dy;
          break;
      }
      if (handleType.includes('w') && width < minWidth) x = originalElement.x + originalElement.width! - minWidth;
      if (handleType.includes('n') && height < minHeight) y = originalElement.y + originalElement.height! - minHeight;

      setElements(prev => {
        let newElements = prev.map(el => el.id === id ? { ...el, x, y, width, height } : el);
        const resizedBox = newElements.find(el => el.id === id);
        if (resizedBox) {
            newElements = updateConnectedLines(resizedBox, newElements);
        }
        return newElements;
      });
      return;
    }


    if (isDrawing && drawingStartPoint && tempElement) {
      let endPoint = { ...currentMousePos }; // Make a copy to modify for constraints

      if (tempElement.type === 'line' || tempElement.type === 'arrow') {
        // Apply constraints for horizontal/vertical line tools
        if (activeTool === 'horizontal-line') {
          endPoint.y = drawingStartPoint.y;
        } else if (activeTool === 'vertical-line') {
          endPoint.x = drawingStartPoint.x;
        }

        // Snapping logic (adjusts the non-constrained part of the endpoint)
        for (const el of elements) {
          if ((el.type === 'box' || el.type === 'diamond') && el.id !== drawingFromNodeInfo?.elementId) {
            const targetNodePositions = getNodePositions(el);
            for (const [nodeType, nodePos] of Object.entries(targetNodePositions)) {
              if (nodePos) {
                // Use the potentially constrained endpoint for distance calculation
                const dist = Math.sqrt(Math.pow(endPoint.x - nodePos.x, 2) + Math.pow(endPoint.y - nodePos.y, 2));
                if (dist < SNAP_DISTANCE / canvasZoom) {
                  if (activeTool === 'horizontal-line') {
                    endPoint.x = nodePos.x; // Snap x, y is already constrained
                  } else if (activeTool === 'vertical-line') {
                    endPoint.y = nodePos.y; // Snap y, x is already constrained
                  } else { // Free line/arrow
                    endPoint = nodePos;
                  }
                  newSnappedToNodeInfo = { elementId: el.id, nodeType: nodeType as ConnectionNodeType };
                  break;
                }
              }
            }
          }
          if (newSnappedToNodeInfo) break;
        }
        setTempElement({ ...tempElement, points: [drawingStartPoint, endPoint] });
      } else if (tempElement.type === 'box') {
        setTempElement({
          ...tempElement,
          width: Math.abs(currentMousePos.x - drawingStartPoint.x),
          height: Math.abs(currentMousePos.y - drawingStartPoint.y),
          x: Math.min(currentMousePos.x, drawingStartPoint.x),
          y: Math.min(currentMousePos.y, drawingStartPoint.y),
        });
      } else if (tempElement.type === 'diamond') {
        setTempElement({
          ...tempElement,
          width: Math.abs(currentMousePos.x - drawingStartPoint.x),
          height: Math.abs(currentMousePos.y - drawingStartPoint.y),
          x: Math.min(currentMousePos.x, drawingStartPoint.x),
          y: Math.min(currentMousePos.y, drawingStartPoint.y),
        });
      } else if (tempElement.type === 'pencil' && tempElement.points) {
        const lastPoint = tempElement.points[tempElement.points.length - 1];
        const dist = Math.sqrt(Math.pow(currentMousePos.x - lastPoint.x, 2) + Math.pow(currentMousePos.y - lastPoint.y, 2));
        if (dist > 2 / canvasZoom) {
            setTempElement({ ...tempElement, points: [...tempElement.points, currentMousePos] });
        }
      }
    } else if (isDraggingElement && selectedElementId && dragStartOffset && selectedElement && originalElementState) {
        if (activeTool !== 'hand') {
            let newX = selectedElement.x;
            let newY = selectedElement.y;
            let newPoints = selectedElement.points;

            if ((selectedElement.type === 'line' || selectedElement.type === 'arrow') && originalElementState.points && originalElementState.points.length === 2) {
                // For lines, calculate movement from original position
                const dx = currentMousePos.x - dragStartOffset.x;
                const dy = currentMousePos.y - dragStartOffset.y;
                newPoints = [
                  { x: originalElementState.points[0].x + dx, y: originalElementState.points[0].y + dy },
                  { x: originalElementState.points[1].x + dx, y: originalElementState.points[1].y + dy },
                ];
            } else if (selectedElement.type === 'pencil' && originalElementState.type === 'pencil') {
                // For pencil, calculate movement from original position
                const dx = currentMousePos.x - dragStartOffset.x;
                const dy = currentMousePos.y - dragStartOffset.y;
                newX = originalElementState.x + dx;
                newY = originalElementState.y + dy;
                // Points stay the same relative to the bounding box
                newPoints = originalElementState.points;
            } else {
                newX = currentMousePos.x - dragStartOffset.x;
                newY = currentMousePos.y - dragStartOffset.y;
            }
            setElements(prev => {
                let newElements = prev.map(el => {
                    if (el.id === selectedElementId) {
                        if (el.type === 'pencil') {
                            return { ...el, x: newX, y: newY, points: newPoints };
                        }
                        if (el.points && el.points.length === 2) return { ...el, points: newPoints as [Point, Point] };
                        return { ...el, x: newX, y: newY };
                    }
                    return el;
                });
                const movedBox = newElements.find(el => el.id === selectedElementId && (el.type === 'box' || el.type === 'diamond'));
                if (movedBox) {
                    newElements = updateConnectedLines(movedBox, newElements);
                }
                return newElements;
            });
        }
    }
    setSnappedToNodeInfo(newSnappedToNodeInfo);
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    if (isPanning) {
      setIsPanning(false);
      return;
    }

    if (isResizing) {
      setIsResizing(false);
      setResizingElementInfo(null);
      return;
    }

    if (isResizingLineEndPoint) {
      const { elementId, pointIndex } = isResizingLineEndPoint;
      setElements(prevElements =>
        prevElements.map(el => {
          if (el.id === elementId && el.points && el.points.length === 2) {
            const updatedEl = { ...el };
            const currentPoints = el.points as [Point, Point];
            if (snappedToNodeInfo) {
              const targetBox = elements.find(b => b.id === snappedToNodeInfo!.elementId);
              if (targetBox) {
                const snappedPosition = getNodePositions(targetBox)?.[snappedToNodeInfo!.nodeType];
                if (snappedPosition) currentPoints[pointIndex] = snappedPosition;
              }

              if (pointIndex === 0) {
                updatedEl.sourceElementId = snappedToNodeInfo.elementId;
                updatedEl.sourceNode = snappedToNodeInfo.nodeType;
              } else {
                updatedEl.targetElementId = snappedToNodeInfo.elementId;
                updatedEl.targetNode = snappedToNodeInfo.nodeType;
              }
            } else {
              if (pointIndex === 0) {
                delete updatedEl.sourceElementId;
                delete updatedEl.sourceNode;
              } else {
                delete updatedEl.targetElementId;
                delete updatedEl.targetNode;
              }
            }
            updatedEl.points = currentPoints;
            return updatedEl;
          }
          return el;
        })
      );
      setIsResizingLineEndPoint(null);
      setSnappedToNodeInfo(null);
      return;
    }

    const pos = getMousePosition(e);
    let elementCreated = false;

    if (isDrawing && tempElement) {
      let finalElement = { ...tempElement };

      if (drawingFromNodeInfo && !snappedToNodeInfo && (tempElement.type === 'arrow' || tempElement.type === 'line')) {
        const newBoxId = uuidv4();
        const newBox: FlowchartElement = {
          id: newBoxId, type: 'box',
          x: pos.x - DEFAULT_NEW_BOX_WIDTH / 2, y: pos.y - DEFAULT_NEW_BOX_HEIGHT / 2,
          width: DEFAULT_NEW_BOX_WIDTH, height: DEFAULT_NEW_BOX_HEIGHT,
          text: 'New Box', fillColor: '#FFFFFF', borderColor: '#000000', fontSize: 16, textColor: '#000000',
        };

        const newBoxCenterX = newBox.x + newBox.width! / 2;
        const newBoxCenterY = newBox.y + newBox.height! / 2;
        const dx = drawingFromNodeInfo.position.x - newBoxCenterX;
        const dy = drawingFromNodeInfo.position.y - newBoxCenterY;
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;
        let targetNodeOnNewBox: ConnectionNodeType;

        if (angle >= -45 && angle < 45) targetNodeOnNewBox = 'right';
        else if (angle >= 45 && angle < 135) targetNodeOnNewBox = 'bottom';
        else if (angle >= 135 || angle < -135) targetNodeOnNewBox = 'left';
        else targetNodeOnNewBox = 'top';

        const targetPositionOnNewBox = getNodePositions(newBox)[targetNodeOnNewBox];
        if (targetPositionOnNewBox && finalElement.points && finalElement.points.length === 2) {
            finalElement = {
                ...finalElement,
                targetElementId: newBoxId,
                targetNode: targetNodeOnNewBox,
                points: [drawingFromNodeInfo.position, targetPositionOnNewBox],
            };
            addElementsWithHistory([newBox, finalElement], `Add connected box`);
            setSelectedElementId(newBoxId);
            elementCreated = true;
        } else {
            addElementsWithHistory([newBox], `Add box`);
            setSelectedElementId(newBoxId);
            elementCreated = true;
        }

      } else if (snappedToNodeInfo && finalElement.points && finalElement.points.length === 2) {
        const targetBox = elements.find(el => el.id === snappedToNodeInfo.elementId);
        if (targetBox) {
            const targetNodeAbsPos = getNodePositions(targetBox)?.[snappedToNodeInfo.nodeType];
            if (targetNodeAbsPos) (finalElement.points as [Point, Point])[1] = targetNodeAbsPos;
        }
        finalElement.targetElementId = snappedToNodeInfo.elementId;
        finalElement.targetNode = snappedToNodeInfo.nodeType;
        if (drawingFromNodeInfo && finalElement.points && finalElement.points.length === 2) {
            (finalElement.points as [Point, Point])[0] = drawingFromNodeInfo.position;
            finalElement.sourceElementId = drawingFromNodeInfo.elementId;
            finalElement.sourceNode = drawingFromNodeInfo.nodeType;
        }
        addElementsWithHistory([finalElement], `Add ${finalElement.type}`);
        setSelectedElementId(finalElement.id);
        elementCreated = true;

      } else if (finalElement.type === 'box' && ((finalElement.width || 0) >= MIN_BOX_SIZE && (finalElement.height || 0) >= MIN_BOX_SIZE) ) {
        addElementsWithHistory([finalElement], `Add ${finalElement.type}`);
        setSelectedElementId(finalElement.id);
        elementCreated = true;
      } else if (finalElement.type === 'diamond' && ((finalElement.width || 0) >= MIN_BOX_SIZE && (finalElement.height || 0) >= MIN_BOX_SIZE) ) {
        addElementsWithHistory([finalElement], `Add ${finalElement.type}`);
        setSelectedElementId(finalElement.id);
        elementCreated = true;
      } else if ((finalElement.type === 'line' || finalElement.type === 'arrow') && finalElement.points && finalElement.points.length === 2 && drawingStartPoint) {
        const p0 = finalElement.points[0]; const p1 = finalElement.points[1];
        const dist = Math.sqrt(Math.pow(p1.x - p0.x, 2) + Math.pow(p1.y - p0.y, 2));
        if (dist >= MIN_LINE_LENGTH) {
            addElementsWithHistory([finalElement], `Add ${finalElement.type}`);
            setSelectedElementId(finalElement.id);
            elementCreated = true;
        }
      } else if (finalElement.type === 'pencil' && finalElement.points && finalElement.points.length >= MIN_PENCIL_PATH_LENGTH) {
        const allX = finalElement.points.map(p => p.x);
        const allY = finalElement.points.map(p => p.y);
        const minX = Math.min(...allX);
        const minY = Math.min(...allY);
        const maxX = Math.max(...allX);
        const maxY = Math.max(...allY);
        finalElement.x = minX;
        finalElement.y = minY;
        finalElement.width = maxX - minX;
        finalElement.height = maxY - minY;
        finalElement.points = finalElement.points.map(p => ({ x: p.x - minX, y: p.y - minY }));

        addElementsWithHistory([finalElement], `Add ${finalElement.type}`);
        setSelectedElementId(finalElement.id);
        elementCreated = true;
      }
    } else if (activeTool === 'text' && drawingStartPoint && !drawingFromNodeInfo) {
        const dist = Math.sqrt(Math.pow(pos.x - drawingStartPoint.x, 2) + Math.pow(pos.y - drawingStartPoint.y, 2));
        if (dist < 5) {
            const newId = uuidv4();
            const newTextElement: FlowchartElement = {
                id: newId, type: 'text', x: drawingStartPoint.x, y: drawingStartPoint.y,
                text: "Text", fontSize: 16, textColor: '#000000', width: DEFAULT_TEXT_WIDTH, height: DEFAULT_TEXT_HEIGHT,
            };
            addElementsWithHistory([newTextElement], `Add text`);
            setSelectedElementId(newId);
            setEditingTextValue(newTextElement.text || "");
            setIsEditingText(true);
            elementCreated = true;
        }
    }

    if (elementCreated) {
        setActiveTool('pointer');
    }

    setIsDrawing(false); setTempElement(null); setDrawingStartPoint(null);
    setDrawingFromNodeInfo(null); setSnappedToNodeInfo(null);
    if (isDraggingElement) { setIsDraggingElement(false); setDragStartOffset(null); setOriginalElementState(null); }
  };

  const handleWheel = (e: React.WheelEvent) => {
    if (!canvasRef.current) return; e.preventDefault();
    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left; const mouseY = e.clientY - rect.top;
    const zoomFactor = 0.1;
    const newZoom = e.deltaY < 0 ? canvasZoom * (1 + zoomFactor) : canvasZoom * (1 - zoomFactor);
    const clampedZoom = Math.max(0.1, Math.min(newZoom, 5));
    const newPanX = mouseX - (mouseX - canvasPan.x) * (clampedZoom / canvasZoom);
    const newPanY = mouseY - (mouseY - canvasPan.y) * (clampedZoom / canvasZoom);
    setCanvasZoom(clampedZoom); setCanvasPan({ x: newPanX, y: newPanY });
  };

  const handleElementMouseDown = (e: React.MouseEvent, elementId: string) => {
    if (activeTool === 'hand') return;
    if (isResizing && resizingElementInfo?.id === elementId) return;
    if (isResizingLineEndPoint && isResizingLineEndPoint.elementId === elementId) return;
    if (isEnteringEditMode) return; // Prevent interference when entering edit mode

    e.stopPropagation();
    const element = elements.find(el => el.id === elementId);
    if (!element) return;

    const pos = getMousePosition(e);

    if (isEditingText && selectedElementId !== elementId) {
        finalizeTextEdit();
    }

    setSelectedElementId(elementId);

    // Robust double-click detection
    const currentTime = Date.now();
    const timeDiff = currentTime - lastClickTime;
    const isDoubleClick = (timeDiff < 300 && lastClickedElementId === elementId) || e.detail === 2;

    console.log('Click detected:', {
      elementId,
      elementType: element.type,
      timeDiff,
      lastClickedElementId,
      isDoubleClick,
      eDetail: e.detail
    });

    setLastClickTime(currentTime);
    setLastClickedElementId(elementId);

    if (activeTool === 'pointer') {
        if (isDoubleClick && (element.type === 'text' || element.type === 'box' || element.type === 'diamond')) {
            console.log('Double-click detected on', element.type, 'element:', element.id);

            // Prevent any interference from canvas mousedown events
            e.preventDefault();
            e.stopPropagation();

            setIsEnteringEditMode(true);
            setIsDraggingElement(false);
            setDragStartOffset(null);
            setEditingTextValue(element.text || "");
            setIsEditingText(true);
            console.log('Text editing enabled, text:', element.text);
            console.log('State after setting:', {
                isEditingText: true,
                selectedElementId: elementId,
                editingTextValue: element.text || ""
            });

            // Clear the flag after a longer delay to ensure state updates complete
            setTimeout(() => {
                setIsEnteringEditMode(false);
                console.log('Cleared isEnteringEditMode flag');
            }, 200);
            return; // Exit early to prevent drag setup
        } else {
            setIsDraggingElement(true);
            setOriginalElementState({ ...element }); // Store original element state
            if ((element.type === 'line' || element.type === 'arrow') && element.points && element.points.length === 2) {
                // For lines, store the mouse position as the drag start reference
                setDragStartOffset({ x: pos.x, y: pos.y });
            } else if (element.type === 'pencil') {
                // For pencil, also store the mouse position as the drag start reference
                setDragStartOffset({ x: pos.x, y: pos.y });
            } else {
                setDragStartOffset({ x: pos.x - element.x, y: pos.y - element.y });
            }
        }
    } else if (isDoubleClick && (element.type === 'text' || element.type === 'box' || element.type === 'diamond')) {
        console.log('Double-click detected (non-pointer tool) on', element.type, 'element:', element.id);
        setEditingTextValue(element.text || "");
        setIsEditingText(true);
        console.log('Text editing enabled, text:', element.text);
    }
  };

  const finalizeTextEdit = () => {
    console.log('finalizeTextEdit called', { isEditingText, selectedElementId, editingTextValue });
    if (isEditingText && selectedElementId) {
        const currentElement = elements.find(el => el.id === selectedElementId);
        if (currentElement && (currentElement.type === 'text' || currentElement.type === 'box' || currentElement.type === 'diamond')) {
            handleUpdateElementProperty('text', editingTextValue);
        }
    }
    setIsEditingText(false);
    setIsEnteringEditMode(false);
  };

  const handleTextChange = (newText: string) => setEditingTextValue(newText);

  const handleExportFlowchart = () => {
    try {
      const flowchartData = {
        elements: elements,
        projectId: projectId,
        exportDate: new Date().toISOString(),
        version: "1.0"
      };

      const dataStr = JSON.stringify(flowchartData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `flowchart-${projectId || 'untitled'}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showSuccess('Flowchart exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      showError('Failed to export flowchart. Please try again.');
    }
  };

  const handleImportFlowchart = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const importedData = JSON.parse(content);

          if (importedData.elements && Array.isArray(importedData.elements)) {
            // Clear existing selection
            setSelectedElementId(null);

            // Set imported elements
            setElements(importedData.elements);

            console.log('Flowchart import successful:', {
              elements: importedData.elements.length,
              firstElement: importedData.elements[0]
            });

            showSuccess(`Successfully imported ${importedData.elements.length} flowchart elements!`);
          } else {
            showError('Invalid flowchart file format. Please select a valid flowchart JSON file.');
          }
        } catch (error) {
          console.error('Import error:', error);
          showError('Failed to import flowchart. Please check the file format.');
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  const handleExportToWireframe = () => {
    const flowchartData: FlowchartImportData = {
      elements: elements,
      projectId: projectId
    };

    const wireframeData: WireframeImportData = importFlowchartToWireframe(flowchartData);

    // Store the wireframe data in localStorage for the wireframe page to pick up
    // This is a simple way to pass data between pages - in a real app you'd use proper state management
    if (wireframeData.pages.length > 0) {
      localStorage.setItem('importedWireframeData', JSON.stringify(wireframeData));

      // Navigate to wireframe page with the same project ID
      const wireframeUrl = projectId ? `/wireframe/${encodeURIComponent(projectId)}` : '/wireframe/imported-project';
      window.location.href = wireframeUrl;
    }
  };

  // Automatic export to wireframe when flowchart elements change
  useEffect(() => {
    // Only auto-export if there are elements with text (potential pages)
    const pageableElements = elements.filter(element =>
      (element.type === 'box' || element.type === 'text') &&
      element.text &&
      element.text.trim() !== ''
    );

    if (pageableElements.length > 0) {
      // Debounce the auto-export to avoid too frequent updates
      const timeoutId = setTimeout(() => {
        const flowchartData: FlowchartImportData = {
          elements: elements,
          projectId: projectId
        };

        const wireframeData: WireframeImportData = importFlowchartToWireframe(flowchartData);

        // Store the wireframe data in localStorage for automatic sync
        if (wireframeData.pages.length > 0) {
          localStorage.setItem('autoSyncWireframeData', JSON.stringify(wireframeData));
          console.log('Auto-synced flowchart to wireframe data');
        }
      }, 1000); // 1 second debounce

      return () => clearTimeout(timeoutId);
    }
  }, [elements, projectId]);

  return (
    <div className="h-screen flex flex-col">
      <div className="p-4 border-b border-divider-lines bg-white flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Flowchart</h1>
          {projectId && <p className="text-md text-creative-blue">Project: {decodeURIComponent(projectId)}</p>}
        </div>
        <div className="flex items-center space-x-2">
          <Button className="secondary-button" onClick={handleImportFlowchart}>
            <Upload className="mr-2 h-4 w-4" /> Import
          </Button>
          <Button className="primary-button" onClick={handleExportFlowchart}>
            <Download className="mr-2 h-4 w-4" /> Export
          </Button>
          {/* <div className="flex items-center text-sm text-creative-blue">
            <ArrowRight className="mr-1 h-4 w-4" />
            Auto-syncing to Wireframe
          </div> */}
        </div>
      </div>
      <div className="flex flex-1 overflow-hidden">
        <FlowchartToolbar activeTool={activeTool} setActiveTool={setActiveTool} />
        <div
          ref={canvasRef}
          className="flex-1 bg-canvas-background relative overflow-hidden"
          onMouseDown={handleMouseDown} onMouseMove={handleMouseMove} onMouseUp={handleMouseUp} onWheel={handleWheel}
          style={{ cursor: isResizing || isResizingLineEndPoint ? 'default' : (activeTool === 'hand' ? (isPanning ? 'grabbing' : 'grab') : (activeTool === 'pointer' ? 'default' : 'crosshair')) }}
        >
          <div style={{ transform: `translate(${canvasPan.x}px, ${canvasPan.y}px) scale(${canvasZoom})`, width: '100%', height: '100%', transformOrigin: 'top left' }}>
            {elements.map((el) => (
              <FlowchartElementDisplay
                key={el.id} element={el} isSelected={el.id === selectedElementId} activeTool={activeTool}
                onElementMouseDown={handleElementMouseDown}
                onNodeMouseDown={handleNodeMouseDown}
                onResizeHandleMouseDown={handleResizeHandleMouseDown}
                onLineEndPointMouseDown={handleLineEndPointMouseDown}
                onExtensionPointClick={handleExtensionPointClick}
                onElementMouseEnter={handleElementMouseEnter}
                onElementMouseLeave={handleElementMouseLeave}
                onExtensionPointMouseEnter={handleExtensionPointMouseEnter}
                onExtensionPointMouseLeave={handleExtensionPointMouseLeave}
                hoveredElementId={hoveredElementId}
                hoveredExtensionPoint={hoveredExtensionPoint}
                isEditing={isEditingText && el.id === selectedElementId} editingText={editingTextValue}
                onTextChange={handleTextChange} onTextBlur={finalizeTextEdit}
                snappedToNodeInfo={snappedToNodeInfo}
              />
            ))}
            {isDrawing && tempElement && (
              <FlowchartElementDisplay
                element={tempElement} isSelected={false} activeTool={activeTool}
                onElementMouseDown={() => {}} isEditing={false} editingText=""
                snappedToNodeInfo={snappedToNodeInfo}
              />
            )}
            {/* Ghost box preview for FigJam-like extension behavior */}
            {hoveredExtensionPoint && (() => {
              const sourceElement = elements.find(el => el.id === hoveredExtensionPoint.elementId);
              if (!sourceElement || (sourceElement.type !== 'box' && sourceElement.type !== 'diamond')) {
                return null;
              }

              // Calculate source position based on extension type
              const getSourcePosition = () => {
                const centerX = sourceElement.x + (sourceElement.width || 0) / 2;
                const centerY = sourceElement.y + (sourceElement.height || 0) / 2;

                switch (hoveredExtensionPoint.extensionType) {
                  case 'top':
                    return { x: centerX, y: sourceElement.y };
                  case 'bottom':
                    return { x: centerX, y: sourceElement.y + (sourceElement.height || 0) };
                  case 'left':
                    return { x: sourceElement.x, y: centerY };
                  case 'right':
                    return { x: sourceElement.x + (sourceElement.width || 0), y: centerY };
                  default:
                    return { x: centerX, y: centerY };
                }
              };

              // Calculate target position (opposite side of ghost box)
              const getTargetPosition = () => {
                const ghostCenterX = hoveredExtensionPoint.position.x;
                const ghostCenterY = hoveredExtensionPoint.position.y;

                switch (hoveredExtensionPoint.extensionType) {
                  case 'top':
                    return { x: ghostCenterX, y: ghostCenterY + DEFAULT_NEW_BOX_HEIGHT / 2 };
                  case 'bottom':
                    return { x: ghostCenterX, y: ghostCenterY - DEFAULT_NEW_BOX_HEIGHT / 2 };
                  case 'left':
                    return { x: ghostCenterX + DEFAULT_NEW_BOX_WIDTH / 2, y: ghostCenterY };
                  case 'right':
                    return { x: ghostCenterX - DEFAULT_NEW_BOX_WIDTH / 2, y: ghostCenterY };
                  default:
                    return { x: ghostCenterX, y: ghostCenterY };
                }
              };

              const sourcePos = getSourcePosition();
              const targetPos = getTargetPosition();

              return (
                <>
                  {/* Ghost connecting arrow */}
                  <svg
                    style={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      width: '100%',
                      height: '100%',
                      pointerEvents: 'none',
                      zIndex: 4,
                      overflow: 'visible',
                    }}
                  >
                    <defs>
                      <marker
                        id="ghost-arrowhead"
                        markerWidth="10"
                        markerHeight="7"
                        refX="9"
                        refY="3.5"
                        orient="auto"
                      >
                        <polygon
                          points="0 0, 10 3.5, 0 7"
                          fill="#2F80ED"
                          opacity="0.7"
                        />
                      </marker>
                    </defs>
                    <line
                      x1={sourcePos.x}
                      y1={sourcePos.y}
                      x2={targetPos.x}
                      y2={targetPos.y}
                      stroke="#2F80ED"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                      opacity="0.7"
                      markerEnd="url(#ghost-arrowhead)"
                    />
                  </svg>

                  {/* Ghost box */}
                  <div
                    style={{
                      position: 'absolute',
                      left: hoveredExtensionPoint.position.x - DEFAULT_NEW_BOX_WIDTH / 2,
                      top: hoveredExtensionPoint.position.y - DEFAULT_NEW_BOX_HEIGHT / 2,
                      width: DEFAULT_NEW_BOX_WIDTH,
                      height: DEFAULT_NEW_BOX_HEIGHT,
                      border: '2px dashed #2F80ED',
                      backgroundColor: 'rgba(47, 128, 237, 0.1)',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '14px',
                      color: '#2F80ED',
                      opacity: 0.7,
                      pointerEvents: 'none',
                      zIndex: 5,
                    }}
                  >
                    New Box
                  </div>
                </>
              );
            })()}
          </div>
        </div>
        <FlowchartSettingsPanel selectedElement={selectedElement} onUpdateProperty={handleUpdateElementProperty} />
      </div>
    </div>
  );
};

// Main wrapper component with HistoryProvider
const FlowchartPage = () => {
  return (
    <HistoryProvider options={{ maxHistorySize: 100, debounceMs: 300 }}>
      <FlowchartPageContent />
    </HistoryProvider>
  );
};

export default FlowchartPage;