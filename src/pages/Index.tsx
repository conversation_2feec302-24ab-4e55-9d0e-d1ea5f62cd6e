// This page can be removed if App.tsx handles the root path directly
// or kept if you want a specific landing experience before redirecting.
// For now, App.tsx redirects "/" to "/projects", so this component might not be rendered.
// If it were, it should ideally render the MainLayout with ProjectsPage.

import ProjectsPage from "./ProjectsPage";

const Index = () => {
  // The MainLayout is now handled in App.tsx at the route level.
  // This component will render ProjectsPage directly if App.tsx was configured
  // to use <Route path="/" element={<Index />} /> within the MainLayout.
  // However, with the current Navigate in App.tsx, this component is bypassed.
  return <ProjectsPage />;
};

export default Index;