import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PlusCircle, Edit, Trash2 } from "lucide-react";
import { CreateProjectModal } from "@/components/CreateProjectModal";

interface Project {
  id: string;
  name: string;
}

// Initial placeholder projects, can be empty array if preferred
const initialProjects: Project[] = [
  { id: "project-alpha", name: "Project Alpha" },
  { id: "project-beta", name: "Project Beta" },
  { id: "project-gamma", name: "Project Gamma" },
];

const ProjectsPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>(initialProjects);

  const handleCreateNewProjectClick = () => {
    setIsModalOpen(true);
  };

  const handleProjectCreated = (newProjectName: string) => {
    const projectId = encodeURIComponent(newProjectName.trim().toLowerCase().replace(/\s+/g, '-'));
    const newProject: Project = {
      id: projectId, // Using a slug-like ID for simplicity
      name: newProjectName.trim(),
    };
    setProjects((prevProjects) => [...prevProjects, newProject]);
    // Modal will handle its own closing and navigation
  };

  const handleEditProject = (projectId: string) => {
    // Implement edit functionality
    console.log(`Edit project: ${projectId}`);
  };

  const handleDeleteProject = (projectId: string) => {
    // Implement delete functionality
    console.log(`Delete project: ${projectId}`);
  };

  return (
    <div className="container mx-auto p-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Projects</h1>
        <Button className="primary-button" onClick={handleCreateNewProjectClick}>
          <PlusCircle className="mr-2 h-5 w-5" />
          Create New Project
        </Button>
      </div>

      <div className="border rounded-lg shadow-sm bg-card">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Project Name
              </TableHead>
              <TableHead className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {projects.map((project) => (
              <TableRow key={project.id} className="hover:bg-muted/50">
                <TableCell className="px-6 py-4 whitespace-nowrap text-sm font-medium text-foreground">
                  {project.name}
                </TableCell>
                <TableCell className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-creative-blue border-creative-blue hover:bg-creative-blue-lighter-bg hover:text-creative-blue"
                    onClick={() => handleEditProject(project.id)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700"
                    onClick={() => handleDeleteProject(project.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {projects.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={2}
                  className="px-6 py-4 text-center text-sm text-muted-foreground"
                >
                  No projects found. Get started by creating a new one!
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <CreateProjectModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  );
};

export default ProjectsPage;