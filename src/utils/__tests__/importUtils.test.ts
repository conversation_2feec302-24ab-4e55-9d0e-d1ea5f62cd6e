import { 
  importFlowchartToWireframe, 
  importWireframeToFlowchart,
  FlowchartImportData,
  WireframeImportData 
} from '../importUtils';
import { FlowchartElement } from '../../pages/FlowchartPage';

// Mock toast functions
jest.mock('../toast', () => ({
  showSuccess: jest.fn(),
  showError: jest.fn()
}));

describe('Import/Export Utils', () => {
  describe('importFlowchartToWireframe', () => {
    test('converts flowchart boxes to wireframe pages', () => {
      const flowchartData: FlowchartImportData = {
        elements: [
          {
            id: 'box1',
            type: 'box',
            x: 100,
            y: 100,
            width: 120,
            height: 60,
            text: 'Login Page',
            fillColor: '#ffffff',
            borderColor: '#000000'
          },
          {
            id: 'box2',
            type: 'box',
            x: 300,
            y: 100,
            width: 120,
            height: 60,
            text: 'Dashboard',
            fillColor: '#ffffff',
            borderColor: '#000000'
          },
          {
            id: 'line1',
            type: 'line',
            x: 0,
            y: 0,
            points: [{ x: 220, y: 130 }, { x: 300, y: 130 }],
            strokeColor: '#000000',
            strokeWidth: 2
          }
        ],
        projectId: 'test-project'
      };

      const result = importFlowchartToWireframe(flowchartData);

      expect(result.pages).toHaveLength(2);
      expect(result.pages[0].name).toBe('Login Page');
      expect(result.pages[1].name).toBe('Dashboard');
      expect(result.projectId).toBe('test-project');
      
      // Check that each page has a starter component
      expect(result.pages[0].components).toHaveLength(1);
      expect(result.pages[0].components[0].text).toBe('Welcome to Login Page');
      expect(result.pages[0].components[0].data?.originalFlowchartElement).toBe('box1');
    });

    test('handles text elements as pages', () => {
      const flowchartData: FlowchartImportData = {
        elements: [
          {
            id: 'text1',
            type: 'text',
            x: 100,
            y: 100,
            width: 100,
            height: 30,
            text: 'Home Page',
            fontSize: 16,
            textColor: '#000000'
          }
        ]
      };

      const result = importFlowchartToWireframe(flowchartData);

      expect(result.pages).toHaveLength(1);
      expect(result.pages[0].name).toBe('Home Page');
    });

    test('sanitizes page names', () => {
      const flowchartData: FlowchartImportData = {
        elements: [
          {
            id: 'box1',
            type: 'box',
            x: 100,
            y: 100,
            width: 120,
            height: 60,
            text: 'Page with @#$% special chars!',
            fillColor: '#ffffff',
            borderColor: '#000000'
          }
        ]
      };

      const result = importFlowchartToWireframe(flowchartData);

      expect(result.pages[0].name).toBe('Page with  special chars');
    });

    test('handles empty or invalid text', () => {
      const flowchartData: FlowchartImportData = {
        elements: [
          {
            id: 'box1',
            type: 'box',
            x: 100,
            y: 100,
            width: 120,
            height: 60,
            text: '',
            fillColor: '#ffffff',
            borderColor: '#000000'
          },
          {
            id: 'box2',
            type: 'box',
            x: 300,
            y: 100,
            width: 120,
            height: 60,
            // No text property
            fillColor: '#ffffff',
            borderColor: '#000000'
          }
        ]
      };

      const result = importFlowchartToWireframe(flowchartData);

      expect(result.pages).toHaveLength(0);
    });

    test('returns empty result for no suitable elements', () => {
      const flowchartData: FlowchartImportData = {
        elements: [
          {
            id: 'line1',
            type: 'line',
            x: 0,
            y: 0,
            points: [{ x: 100, y: 100 }, { x: 200, y: 200 }],
            strokeColor: '#000000',
            strokeWidth: 2
          }
        ]
      };

      const result = importFlowchartToWireframe(flowchartData);

      expect(result.pages).toHaveLength(0);
    });
  });

  describe('importWireframeToFlowchart', () => {
    test('converts wireframe pages to flowchart elements', () => {
      const wireframeData: WireframeImportData = {
        pages: [
          {
            id: 'page1',
            name: 'Login Page',
            components: [
              {
                id: 'comp1',
                type: 'Button',
                x: 50,
                y: 50,
                width: 100,
                height: 40,
                text: 'Login',
                data: {}
              }
            ]
          },
          {
            id: 'page2',
            name: 'Dashboard',
            components: []
          }
        ],
        projectId: 'test-project'
      };

      const result = importWireframeToFlowchart(wireframeData);

      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Login Page');
      expect(result[1].text).toBe('Dashboard');
      expect(result[0].type).toBe('box');
      expect(result[0].width).toBe(150);
      expect(result[0].height).toBe(80);
    });

    test('arranges elements in a grid', () => {
      const wireframeData: WireframeImportData = {
        pages: Array.from({ length: 6 }, (_, i) => ({
          id: `page${i + 1}`,
          name: `Page ${i + 1}`,
          components: []
        }))
      };

      const result = importWireframeToFlowchart(wireframeData);

      expect(result).toHaveLength(6);
      
      // Check grid arrangement (3 columns)
      expect(result[0].x).toBe(100); // First row, first column
      expect(result[1].x).toBe(300); // First row, second column
      expect(result[2].x).toBe(500); // First row, third column
      expect(result[3].x).toBe(100); // Second row, first column
      expect(result[3].y).toBe(250); // Second row
    });

    test('returns empty array for no pages', () => {
      const wireframeData: WireframeImportData = {
        pages: []
      };

      const result = importWireframeToFlowchart(wireframeData);

      expect(result).toHaveLength(0);
    });

    test('handles error gracefully', () => {
      // Pass invalid data to trigger error handling
      const result = importWireframeToFlowchart(null as any);

      expect(result).toHaveLength(0);
    });
  });
});
