import { showSuccess, showError } from "@/utils/toast";
import { FlowchartElement } from "@/pages/FlowchartPage";
import { WireframeComponent } from "@/components/wireframe/WireframePropertiesPanel";

export interface WireframePageData {
  id: string;
  name: string;
  components: WireframeComponent[];
}

export interface FlowchartImportData {
  elements: FlowchartElement[];
  projectId?: string;
}

export interface WireframeImportData {
  pages: WireframePageData[];
  projectId?: string;
}

// Helper function to sanitize text for page names
const sanitizePageName = (text: string): string => {
  if (!text || text.trim() === '') {
    return 'Untitled Page';
  }
  // Remove special characters and limit length
  return text.trim().substring(0, 50).replace(/[^a-zA-Z0-9\s-_]/g, '');
};

// Helper function to generate unique page ID
const generatePageId = (): string => {
  return `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const importFlowchartToWireframe = (flowchartData: FlowchartImportData): WireframeImportData => {
  try {
    console.log("Importing flowchart to wireframe:", flowchartData);

    // Filter flowchart elements to get only boxes and text elements that can become pages
    const pageElements = flowchartData.elements.filter(element =>
      (element.type === 'box' || element.type === 'text') &&
      element.text &&
      element.text.trim() !== ''
    );

    if (pageElements.length === 0) {
      showError("No suitable elements found for wireframe pages. Please add boxes or text elements with names.");
      return { pages: [], projectId: flowchartData.projectId };
    }

    // Convert flowchart elements to wireframe pages
    const wireframePages: WireframePageData[] = pageElements.map((element, index) => {
      const pageName = sanitizePageName(element.text || `Page ${index + 1}`);

      // Create a basic starter component for each page
      const starterComponent: WireframeComponent = {
        id: `starter_${Date.now()}_${index}`,
        type: 'Text',
        x: 50,
        y: 50,
        width: 200,
        height: 40,
        text: `Welcome to ${pageName}`,
        data: {
          placeholder: true,
          originalFlowchartElement: element.id
        }
      };

      return {
        id: generatePageId(),
        name: pageName,
        components: [starterComponent]
      };
    });

    console.log("Generated wireframe pages:", wireframePages);

    const result: WireframeImportData = {
      pages: wireframePages,
      projectId: flowchartData.projectId
    };

    showSuccess(`Successfully imported ${wireframePages.length} pages from flowchart!`);
    return result;

  } catch (error) {
    console.error("Error importing flowchart to wireframe:", error);
    showError("Failed to import flowchart to wireframe. Please try again.");
    return { pages: [], projectId: flowchartData.projectId };
  }
};

export const importWireframeToFlowchart = (wireframeData: WireframeImportData): FlowchartElement[] => {
  try {
    console.log("Importing wireframe to flowchart:", wireframeData);

    if (!wireframeData.pages || wireframeData.pages.length === 0) {
      showError("No wireframe pages found to import.");
      return [];
    }

    // Convert wireframe pages to flowchart box elements
    const flowchartElements: FlowchartElement[] = wireframeData.pages.map((page, index) => {
      const xPosition = 100 + (index % 3) * 200; // Arrange in a grid
      const yPosition = 100 + Math.floor(index / 3) * 150;

      const element: FlowchartElement = {
        id: `imported_${Date.now()}_${index}`,
        type: 'box',
        x: xPosition,
        y: yPosition,
        width: 150,
        height: 80,
        text: page.name,
        fontSize: 14,
        textColor: '#333333',
        fillColor: '#f8f9fa',
        borderColor: '#6c757d'
      };

      return element;
    });

    console.log("Generated flowchart elements:", flowchartElements);
    showSuccess(`Successfully imported ${flowchartElements.length} pages as flowchart elements!`);
    return flowchartElements;

  } catch (error) {
    console.error("Error importing wireframe to flowchart:", error);
    showError("Failed to import wireframe to flowchart. Please try again.");
    return [];
  }
};