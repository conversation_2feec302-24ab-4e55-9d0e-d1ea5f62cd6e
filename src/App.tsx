import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { MainLayout } from "./components/MainLayout";
import ProjectsPage from "./pages/ProjectsPage";
import FlowchartPage from "./pages/FlowchartPage";
import WireframePage from "./pages/WireframePage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route element={<MainLayout />}>
            <Route path="/" element={<Navigate to="/projects" replace />} />
            <Route path="/projects" element={<ProjectsPage />} />
            <Route path="/flowchart" element={<FlowchartPage />} />
            {/* Dynamic route for specific flowchart project */}
            <Route path="/flowchart/:projectId" element={<FlowchartPage />} /> 
            <Route path="/wireframe" element={<WireframePage />} />
            {/* Dynamic route for specific wireframe project */}
            <Route path="/wireframe/:projectId" element={<WireframePage />} />
            <Route path="/*" element={<NotFound />} />
          </Route>
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;