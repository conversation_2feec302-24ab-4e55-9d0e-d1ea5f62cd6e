import { useState, useCallback, useRef } from 'react';

export interface HistoryAction<T> {
  type: string;
  description: string;
  timestamp: number;
  undo: () => void;
  redo: () => void;
  data?: T;
}

export interface HistoryState<T> {
  canUndo: boolean;
  canRedo: boolean;
  currentIndex: number;
  history: HistoryAction<T>[];
  lastAction?: HistoryAction<T>;
}

export interface UseHistoryOptions {
  maxHistorySize?: number;
  debounceMs?: number;
}

export function useHistory<T = any>(options: UseHistoryOptions = {}) {
  const { maxHistorySize = 50, debounceMs = 300 } = options;
  
  const [history, setHistory] = useState<HistoryAction<T>[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActionTypeRef = useRef<string | null>(null);

  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < history.length - 1;
  const lastAction = history[currentIndex] || undefined;

  const addAction = useCallback((action: Omit<HistoryAction<T>, 'timestamp'>) => {
    const newAction: HistoryAction<T> = {
      ...action,
      timestamp: Date.now(),
    };

    setHistory(prevHistory => {
      // Remove any actions after current index (when we're in the middle of history)
      const newHistory = prevHistory.slice(0, currentIndex + 1);
      
      // Add the new action
      newHistory.push(newAction);
      
      // Limit history size
      if (newHistory.length > maxHistorySize) {
        return newHistory.slice(-maxHistorySize);
      }
      
      return newHistory;
    });

    setCurrentIndex(prevIndex => {
      const newIndex = Math.min(prevIndex + 1, maxHistorySize - 1);
      return newIndex;
    });

    lastActionTypeRef.current = action.type;
  }, [currentIndex, maxHistorySize]);

  const addDebouncedAction = useCallback((action: Omit<HistoryAction<T>, 'timestamp'>) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // If this is the same type of action as the last one, debounce it
    if (lastActionTypeRef.current === action.type) {
      debounceTimeoutRef.current = setTimeout(() => {
        addAction(action);
      }, debounceMs);
    } else {
      // Different action type, add immediately
      addAction(action);
    }
  }, [addAction, debounceMs]);

  const undo = useCallback(() => {
    if (!canUndo) return false;

    const action = history[currentIndex];
    if (action) {
      try {
        action.undo();
        setCurrentIndex(prev => prev - 1);
        return true;
      } catch (error) {
        console.error('Error during undo:', error);
        return false;
      }
    }
    return false;
  }, [canUndo, currentIndex, history]);

  const redo = useCallback(() => {
    if (!canRedo) return false;

    const action = history[currentIndex + 1];
    if (action) {
      try {
        action.redo();
        setCurrentIndex(prev => prev + 1);
        return true;
      } catch (error) {
        console.error('Error during redo:', error);
        return false;
      }
    }
    return false;
  }, [canRedo, currentIndex, history]);

  const clear = useCallback(() => {
    setHistory([]);
    setCurrentIndex(-1);
    lastActionTypeRef.current = null;
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
  }, []);

  const getHistoryState = useCallback((): HistoryState<T> => ({
    canUndo,
    canRedo,
    currentIndex,
    history,
    lastAction,
  }), [canUndo, canRedo, currentIndex, history, lastAction]);

  return {
    addAction,
    addDebouncedAction,
    undo,
    redo,
    clear,
    canUndo,
    canRedo,
    history,
    currentIndex,
    lastAction,
    getHistoryState,
  };
}

// Helper function to create common action types
export const createAction = <T>(
  type: string,
  description: string,
  undoFn: () => void,
  redoFn: () => void,
  data?: T
): Omit<HistoryAction<T>, 'timestamp'> => ({
  type,
  description,
  undo: undoFn,
  redo: redoFn,
  data,
});

// Common action types
export const ACTION_TYPES = {
  ADD_COMPONENT: 'ADD_COMPONENT',
  DELETE_COMPONENT: 'DELETE_COMPONENT',
  MOVE_COMPONENT: 'MOVE_COMPONENT',
  RESIZE_COMPONENT: 'RESIZE_COMPONENT',
  UPDATE_COMPONENT: 'UPDATE_COMPONENT',
  ADD_ELEMENT: 'ADD_ELEMENT',
  DELETE_ELEMENT: 'DELETE_ELEMENT',
  MOVE_ELEMENT: 'MOVE_ELEMENT',
  UPDATE_ELEMENT: 'UPDATE_ELEMENT',
} as const;
