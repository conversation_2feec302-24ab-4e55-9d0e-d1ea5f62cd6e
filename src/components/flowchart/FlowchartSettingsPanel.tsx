import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { AlignLeft, AlignCenter, AlignRight } from 'lucide-react';
import { FlowchartElement } from '@/pages/FlowchartPage'; // Import the element type

interface FlowchartSettingsPanelProps {
  selectedElement: FlowchartElement | null;
  onUpdateProperty: (propertyName: keyof FlowchartElement, value: any) => void;
}

const FONT_SIZES = [
    { label: 'S', value: 12 },
    { label: 'M', value: 16 },
    { label: 'L', value: 20 },
    { label: 'XL', value: 24 },
];

export const FlowchartSettingsPanel: React.FC<FlowchartSettingsPanelProps> = ({ selectedElement, onUpdateProperty }) => {
  if (!selectedElement) {
    return (
      <div className="w-64 bg-panel-background border-l border-divider-lines p-4 flex flex-col h-full overflow-y-auto">
        <div className="text-sm text-gray-500 text-center py-10">
          <p>Select an item on the canvas to see its properties, or choose a tool to create an item.</p>
        </div>
      </div>
    );
  }

  const handleNumericInput = (property: keyof FlowchartElement, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      onUpdateProperty(property, numValue);
    }
  };
  
  const selectedElementType = selectedElement.type;

  return (
    <div className="w-64 bg-panel-background border-l border-divider-lines p-4 flex flex-col space-y-6 h-full overflow-y-auto">
      <div>
        <h3 className="text-lg font-semibold mb-2 text-gray-700 capitalize">{selectedElementType} Settings</h3>
        <Separator />
      </div>

      {selectedElementType === 'text' && (
        <>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-600">Font Size</Label>
              <div className="flex space-x-1 mt-1">
                {FONT_SIZES.map(size => (
                  <Button 
                    key={size.label} 
                    variant={selectedElement.fontSize === size.value ? "default" : "outline"} 
                    size="sm" 
                    className="flex-1 hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white"
                    onClick={() => onUpdateProperty('fontSize', size.value)}
                  >
                    {size.label}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="textColor" className="text-sm font-medium text-gray-600">Text Color</Label>
              <Input
                type="color"
                id="textColor"
                value={selectedElement.textColor || '#000000'}
                className="mt-1 w-full h-10 p-1 border-gray-300 rounded-md cursor-pointer"
                onChange={(e) => onUpdateProperty('textColor', e.target.value)}
              />
            </div>
            {/* Placeholder for Alignment - requires more complex state/logic for text rendering */}
            {/* <div>
              <Label className="text-sm font-medium text-gray-600">Alignment</Label>
              <div className="flex space-x-1 mt-1">
                <Button variant="outline" size="icon" className="hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white">
                  <AlignLeft size={18}/>
                </Button>
              </div>
            </div> */}
          </div>
          <Separator />
        </>
      )}

      {selectedElementType === 'box' && (
        <>
          <div className="space-y-4">
             {/* Text specific settings can be included here if a box has text */}
             <div>
              <Label className="text-sm font-medium text-gray-600">Font Size (Text in Box)</Label>
              <div className="flex space-x-1 mt-1">
                {FONT_SIZES.map(size => (
                  <Button 
                    key={size.label} 
                    variant={selectedElement.fontSize === size.value ? "default" : "outline"} 
                    size="sm" 
                    className="flex-1 hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white"
                    onClick={() => onUpdateProperty('fontSize', size.value)}
                  >
                    {size.label}
                  </Button>
                ))}
              </div>
            </div>
            <div>
              <Label htmlFor="boxTextColor" className="text-sm font-medium text-gray-600">Text Color</Label>
              <Input
                type="color"
                id="boxTextColor"
                value={selectedElement.textColor || '#000000'}
                className="mt-1 w-full h-10 p-1 border-gray-300 rounded-md cursor-pointer"
                onChange={(e) => onUpdateProperty('textColor', e.target.value)}
              />
            </div>
            <Separator className="my-2"/>
            <div>
              <Label htmlFor="boxFillColor" className="text-sm font-medium text-gray-600">Fill Color</Label>
              <Input 
                type="color" 
                id="boxFillColor" 
                value={selectedElement.fillColor || '#FFFFFF'}
                className="mt-1 w-full h-10 p-1 border-gray-300 rounded-md cursor-pointer"
                onChange={(e) => onUpdateProperty('fillColor', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="boxBorderColor" className="text-sm font-medium text-gray-600">Border Color</Label>
              <Input 
                type="color" 
                id="boxBorderColor" 
                value={selectedElement.borderColor || '#000000'}
                className="mt-1 w-full h-10 p-1 border-gray-300 rounded-md cursor-pointer"
                onChange={(e) => onUpdateProperty('borderColor', e.target.value)}
              />
            </div>
          </div>
          <Separator />
        </>
      )}
      
      {(selectedElementType === 'line' || selectedElementType === 'arrow') && (
        <>
          <div className="space-y-4">
            <div>
              <Label htmlFor="lineStrokeColor" className="text-sm font-medium text-gray-600">Line Color</Label>
              <Input 
                type="color" 
                id="lineStrokeColor" 
                value={selectedElement.strokeColor || '#000000'}
                className="mt-1 w-full h-10 p-1 border-gray-300 rounded-md cursor-pointer"
                onChange={(e) => onUpdateProperty('strokeColor', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="lineStrokeWidth" className="text-sm font-medium text-gray-600">Line Thickness</Label>
              <Input 
                type="number" 
                id="lineStrokeWidth" 
                value={selectedElement.strokeWidth || 2}
                min="1"
                max="20"
                className="mt-1 w-full h-10 p-2 border-gray-300 rounded-md" // Adjusted padding
                onChange={(e) => handleNumericInput('strokeWidth', e.target.value)}
              />
            </div>
          </div>
          <Separator />
        </>
      )}
    </div>
  );
};