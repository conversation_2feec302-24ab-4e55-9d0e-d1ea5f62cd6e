import React from 'react';
import { FlowchartElement, Point } from '@/pages/FlowchartPage';

export type ConnectionNodeType = 'top' | 'bottom' | 'left' | 'right';
export type ResizeHandleType = 'n' | 's' | 'e' | 'w' | 'ne' | 'nw' | 'se' | 'sw';
export type ExtensionPointType = 'top' | 'bottom' | 'left' | 'right';

export interface SnappedNodeInfo {
  elementId: string;
  nodeType: ConnectionNodeType;
}

interface FlowchartElementDisplayProps {
  element: FlowchartElement;
  isSelected: boolean;
  activeTool?: string;
  onElementMouseDown: (e: React.MouseEvent, elementId: string) => void;
  onNodeMouseDown?: (
    e: React.MouseEvent,
    sourceElementId: string,
    nodeType: ConnectionNodeType,
    nodePosition: Point
  ) => void;
  onResizeHandleMouseDown?: (
    e: React.MouseEvent,
    elementId: string,
    handleType: ResizeHandleType
  ) => void;
  onLineEndPointMouseDown?: (
    e: React.MouseEvent,
    elementId: string,
    pointIndex: 0 | 1
  ) => void;
  onExtensionPointClick?: (
    e: React.MouseEvent,
    sourceElementId: string,
    extensionType: ExtensionPointType,
    extensionPosition: Point
  ) => void;
  onElementMouseEnter?: (elementId: string) => void;
  onElementMouseLeave?: () => void;
  onExtensionPointMouseEnter?: (
    elementId: string,
    extensionType: ExtensionPointType,
    position: Point
  ) => void;
  onExtensionPointMouseLeave?: () => void;
  hoveredElementId?: string | null;
  hoveredExtensionPoint?: {
    elementId: string;
    extensionType: ExtensionPointType;
    position: Point;
  } | null;
  isEditing: boolean;
  editingText: string;
  onTextChange?: (newText: string) => void;
  onTextBlur?: () => void;
  snappedToNodeInfo?: SnappedNodeInfo | null;
}

const MIN_INTERACTIVE_SIZE = 4;
const NODE_SIZE = 8;
const RESIZE_HANDLE_SIZE = 8;
const LINE_ENDPOINT_HANDLE_SIZE = 8;
const EXTENSION_POINT_SIZE = 24;
const EXTENSION_POINT_OFFSET = 16;

// Function to convert an array of points to an SVG path string
const pointsToPath = (points: Point[]): string => {
  if (!points || points.length === 0) return "";
  let path = `M ${points[0].x} ${points[0].y}`;
  for (let i = 1; i < points.length; i++) {
    path += ` L ${points[i].x} ${points[i].y}`;
  }
  return path;
};

export const FlowchartElementDisplay: React.FC<FlowchartElementDisplayProps> = ({
  element,
  isSelected,
  activeTool,
  onElementMouseDown,
  onNodeMouseDown,
  onResizeHandleMouseDown,
  onLineEndPointMouseDown,
  onExtensionPointClick,
  onElementMouseEnter,
  onElementMouseLeave,
  onExtensionPointMouseEnter,
  onExtensionPointMouseLeave,
  hoveredElementId,
  hoveredExtensionPoint,
  isEditing,
  editingText,
  onTextChange,
  onTextBlur,
  snappedToNodeInfo,
}) => {
  const handleLocalMouseDown = (e: React.MouseEvent) => {
    onElementMouseDown(e, element.id);
  };

  const handleLocalMouseEnter = () => {
    if (onElementMouseEnter) {
      onElementMouseEnter(element.id);
    }
  };

  const handleLocalMouseLeave = () => {
    if (onElementMouseLeave) {
      onElementMouseLeave();
    }
  };

  const handleLocalNodeMouseDown = (
    e: React.MouseEvent,
    nodeType: ConnectionNodeType,
    nodePosition: Point
  ) => {
    if (onNodeMouseDown) {
      // Allow double-clicks to pass through to the main element for text editing
      if (e.detail === 2) {
        onElementMouseDown(e, element.id);
        return;
      }
      e.stopPropagation();
      onNodeMouseDown(e, element.id, nodeType, nodePosition);
    }
  };

  const handleLocalResizeHandleMouseDown = (
    e: React.MouseEvent,
    handleType: ResizeHandleType
  ) => {
    if (onResizeHandleMouseDown) {
      // Allow double-clicks to pass through to the main element for text editing
      if (e.detail === 2) {
        onElementMouseDown(e, element.id);
        return;
      }
      e.stopPropagation();
      onResizeHandleMouseDown(e, element.id, handleType);
    }
  };

  const handleLocalLineEndPointMouseDown = (
    e: React.MouseEvent,
    pointIndex: 0 | 1
  ) => {
    if (onLineEndPointMouseDown) {
      e.stopPropagation();
      onLineEndPointMouseDown(e, element.id, pointIndex);
    }
  };

  const handleExtensionPointClick = (
    e: React.MouseEvent,
    extensionType: ExtensionPointType,
    extensionPosition: Point
  ) => {
    if (onExtensionPointClick) {
      e.stopPropagation();
      onExtensionPointClick(e, element.id, extensionType, extensionPosition);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onTextChange) onTextChange(e.target.value);
  };

  const handleInputBlur = () => {
    if (onTextBlur) onTextBlur();
  };

  if ((element.type === 'line' || element.type === 'arrow') && element.points && element.points.length === 2) {
    const [p1, p2] = element.points as [Point, Point]; // Assert as tuple for line/arrow
    const minX = Math.min(p1.x, p2.x);
    const minY = Math.min(p1.y, p2.y);
    const maxX = Math.max(p1.x, p2.x);
    const maxY = Math.max(p1.y, p2.y);

    const width = Math.max(maxX - minX, MIN_INTERACTIVE_SIZE);
    const height = Math.max(maxY - minY, MIN_INTERACTIVE_SIZE);

    const svgX1 = p1.x - minX;
    const svgY1 = p1.y - minY;
    const svgX2 = p2.x - minX;
    const svgY2 = p2.y - minY;

    const lineContainerStyle: React.CSSProperties = {
      position: 'absolute', left: minX, top: minY, width: width, height: height,
      cursor: activeTool === 'pointer' ? 'grab' : 'default',
      border: isSelected && activeTool === 'pointer' ? `2px dashed var(--creative-blue)` : 'none',
      borderRadius: '4px',
      boxSizing: 'content-box',
      overflow: 'visible',
      // Make the line more clickable by adding padding
      padding: '4px',
      margin: '-4px',
    };
    const arrowId = `arrowhead-${element.id}`;

    return (
      <div style={lineContainerStyle} onMouseDown={handleLocalMouseDown}>
        <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
          {element.type === 'arrow' && (
            <defs>
              <marker id={arrowId} markerWidth="10" markerHeight="7" refX="8" refY="3.5" orient="auto" markerUnits="strokeWidth">
                <polygon points="0 0, 10 3.5, 0 7" fill={element.strokeColor || 'black'} />
              </marker>
            </defs>
          )}
          {/* Invisible thicker line for easier clicking */}
          <line
            x1={svgX1} y1={svgY1} x2={svgX2} y2={svgY2}
            stroke="transparent"
            strokeWidth="12"
            style={{ cursor: activeTool === 'pointer' ? 'grab' : 'default' }}
          />
          {/* Visible line */}
          <line
            x1={svgX1} y1={svgY1} x2={svgX2} y2={svgY2}
            stroke={element.strokeColor || 'black'}
            strokeWidth={element.strokeWidth || 2}
            markerEnd={element.type === 'arrow' ? `url(#${arrowId})` : undefined}
            style={{ pointerEvents: 'none' }}
          />
        </svg>
        {isSelected && activeTool === 'pointer' && onLineEndPointMouseDown && (
          <>
            <div
              style={{
                position: 'absolute',
                left: `${svgX1 - LINE_ENDPOINT_HANDLE_SIZE / 2}px`,
                top: `${svgY1 - LINE_ENDPOINT_HANDLE_SIZE / 2}px`,
                width: `${LINE_ENDPOINT_HANDLE_SIZE}px`,
                height: `${LINE_ENDPOINT_HANDLE_SIZE}px`,
                backgroundColor: 'white',
                border: '1px solid var(--creative-blue)',
                borderRadius: '50%',
                cursor: 'crosshair',
                zIndex: 12,
              }}
              onMouseDown={(e) => handleLocalLineEndPointMouseDown(e, 0)}
            />
            <div
              style={{
                position: 'absolute',
                left: `${svgX2 - LINE_ENDPOINT_HANDLE_SIZE / 2}px`,
                top: `${svgY2 - LINE_ENDPOINT_HANDLE_SIZE / 2}px`,
                width: `${LINE_ENDPOINT_HANDLE_SIZE}px`,
                height: `${LINE_ENDPOINT_HANDLE_SIZE}px`,
                backgroundColor: 'white',
                border: '1px solid var(--creative-blue)',
                borderRadius: '50%',
                cursor: 'crosshair',
                zIndex: 12,
              }}
              onMouseDown={(e) => handleLocalLineEndPointMouseDown(e, 1)}
            />
          </>
        )}
      </div>
    );
  }

  if (element.type === 'pencil' && element.points && element.points.length > 0) {
    const pathData = pointsToPath(element.points);

    // For temp elements during drawing (x=0, y=0, width=1, height=1), render directly
    const isDrawing = element.width === 1 && element.height === 1 && element.x === 0 && element.y === 0;

    if (isDrawing) {
      return (
        <svg
          style={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            overflow: 'visible',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        >
          <path
            d={pathData}
            stroke={element.strokeColor || 'black'}
            strokeWidth={element.strokeWidth || 2}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
    }

    // For finalized elements, use normal container positioning
    const pencilContainerStyle: React.CSSProperties = {
      position: 'absolute',
      left: element.x, // Bounding box top-left
      top: element.y,  // Bounding box top-left
      width: element.width,
      height: element.height,
      cursor: activeTool === 'pointer' ? 'grab' : 'default',
      border: isSelected && activeTool === 'pointer' ? `2px dashed var(--creative-blue)` : 'none',
      borderRadius: '4px',
      boxSizing: 'content-box',
      overflow: 'visible',
      padding: '4px',
      margin: '-4px',
    };

    return (
      <div style={pencilContainerStyle} onMouseDown={handleLocalMouseDown}>
        <svg width={element.width} height={element.height} style={{ overflow: 'visible' }}>
          <path
            d={pathData}
            stroke={element.strokeColor || 'black'}
            strokeWidth={element.strokeWidth || 2}
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    );
  }


  // Determine cursor based on tool and element type
  const getCursor = () => {
    // If currently editing, show text cursor
    if (isEditing) {
      return 'text';
    }

    if (activeTool === 'pointer') {
      // Show grab cursor for draggable elements
      return 'grab';
    }
    if (activeTool === 'box' && isSelected) {
      return 'default';
    }
    return 'default';
  };

  const style: React.CSSProperties = {
    position: 'absolute', left: element.x, top: element.y,
    width: element.width, height: element.height,
    border: `2px solid ${isSelected ? 'var(--creative-blue)' : (element.borderColor || (element.type === 'text' ? 'transparent' : 'black'))}`,
    backgroundColor: element.fillColor || (element.type === 'box' || element.type === 'diamond' ? 'white' : 'transparent'),
    cursor: getCursor(),
    userSelect: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center',
    padding: element.type === 'box' || element.type === 'diamond' ? '5px' : '0', boxSizing: 'border-box',
    fontSize: element.fontSize || 16, color: element.textColor || 'black',
  };

  // Add diamond-specific styling
  if (element.type === 'diamond') {
    style.transform = 'rotate(45deg)';
    style.borderRadius = '8px';
  }

  if (element.type === 'text') {
    if (!element.height) style.height = 'auto';
    if (!element.width) { style.width = 'auto'; style.minWidth = '50px'; }
    if (!isSelected && !isEditing) { style.border = '2px solid transparent'; }
     else if (isSelected) { style.border = '2px solid var(--creative-blue)';}
     else {style.border = '2px solid transparent';}
  }


  const getNodeStyle = (nodeType: ConnectionNodeType): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'absolute', width: `${NODE_SIZE}px`, height: `${NODE_SIZE}px`,
      backgroundColor: 'var(--creative-blue)', border: '1px solid white',
      borderRadius: '50%', boxShadow: '0 0 2px rgba(0,0,0,0.5)', cursor: 'crosshair', zIndex: 10,
    };
    if (snappedToNodeInfo?.elementId === element.id && snappedToNodeInfo?.nodeType === nodeType) {
      baseStyle.backgroundColor = 'var(--creative-blue-darker)'; baseStyle.transform = 'scale(1.2)';
    }
    return baseStyle;
  };

  const nodePositions = {
    top: { x: element.x + (element.width || 0) / 2, y: element.y },
    bottom: { x: element.x + (element.width || 0) / 2, y: element.y + (element.height || 0) },
    left: { x: element.x, y: element.y + (element.height || 0) / 2 },
    right: { x: element.x + (element.width || 0), y: element.y + (element.height || 0) / 2 },
  };

  const resizeHandleBaseStyle: React.CSSProperties = {
    position: 'absolute',
    width: `${RESIZE_HANDLE_SIZE}px`,
    height: `${RESIZE_HANDLE_SIZE}px`,
    backgroundColor: 'white',
    border: '1px solid var(--creative-blue)',
    zIndex: 11,
  };

  const resizeHandles: { type: ResizeHandleType; style: React.CSSProperties }[] =
    (element.type === 'box' || element.type === 'text' || element.type === 'diamond') && isSelected && activeTool === 'pointer' && onResizeHandleMouseDown ? [
    { type: 'nw', style: { ...resizeHandleBaseStyle, top: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nwse-resize' } },
    { type: 'ne', style: { ...resizeHandleBaseStyle, top: `-${RESIZE_HANDLE_SIZE / 2}px`, right: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nesw-resize' } },
    { type: 'sw', style: { ...resizeHandleBaseStyle, bottom: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nesw-resize' } },
    { type: 'se', style: { ...resizeHandleBaseStyle, bottom: `-${RESIZE_HANDLE_SIZE / 2}px`, right: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nwse-resize' } },
    { type: 'n', style: { ...resizeHandleBaseStyle, top: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `calc(50% - ${RESIZE_HANDLE_SIZE / 2}px)`, cursor: 'ns-resize' } },
    { type: 's', style: { ...resizeHandleBaseStyle, bottom: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `calc(50% - ${RESIZE_HANDLE_SIZE / 2}px)`, cursor: 'ns-resize' } },
    { type: 'e', style: { ...resizeHandleBaseStyle, right: `-${RESIZE_HANDLE_SIZE / 2}px`, top: `calc(50% - ${RESIZE_HANDLE_SIZE / 2}px)`, cursor: 'ew-resize' } },
    { type: 'w', style: { ...resizeHandleBaseStyle, left: `-${RESIZE_HANDLE_SIZE / 2}px`, top: `calc(50% - ${RESIZE_HANDLE_SIZE / 2}px)`, cursor: 'ew-resize' } },
  ] : [];




  return (
    <div
      style={style}
      onMouseDown={handleLocalMouseDown}
      onMouseEnter={handleLocalMouseEnter}
      onMouseLeave={handleLocalMouseLeave}
      data-component-id={element.id}
    >
      {isEditing && (element.type === 'text' || element.type === 'box' || element.type === 'diamond') ? (
        <textarea
          value={editingText} onChange={handleInputChange} onBlur={handleInputBlur} autoFocus
          style={{
            width: '100%', height: '100%', border: 'none', outline: 'none', resize: 'none',
            textAlign: 'center', backgroundColor: 'transparent', fontSize: 'inherit',
            color: 'inherit', fontFamily: 'inherit', boxSizing: 'border-box', padding: 0, margin: 0,
            transform: element.type === 'diamond' ? 'rotate(-45deg)' : 'none', // Counter-rotate text in diamond
          }}
          onMouseDown={(e) => e.stopPropagation()}
        />
      ) : (
        <span style={{
          transform: element.type === 'diamond' ? 'rotate(-45deg)' : 'none',
          display: 'inline-block'
        }}>
          {element.text || ''}
        </span>
      )}

      {(element.type === 'box' || element.type === 'diamond') && isSelected && element.width && element.height && onNodeMouseDown && (activeTool === 'pointer' || activeTool === 'line' || activeTool === 'arrow') && (
        <>
          <div style={{ ...getNodeStyle('top'), top: `-${NODE_SIZE / 2}px`, left: `calc(50% - ${NODE_SIZE / 2}px)` }} onMouseDown={(e) => handleLocalNodeMouseDown(e, 'top', nodePositions.top)} />
          <div style={{ ...getNodeStyle('bottom'), bottom: `-${NODE_SIZE / 2}px`, left: `calc(50% - ${NODE_SIZE / 2}px)` }} onMouseDown={(e) => handleLocalNodeMouseDown(e, 'bottom', nodePositions.bottom)} />
          <div style={{ ...getNodeStyle('left'), left: `-${NODE_SIZE / 2}px`, top: `calc(50% - ${NODE_SIZE / 2}px)` }} onMouseDown={(e) => handleLocalNodeMouseDown(e, 'left', nodePositions.left)} />
          <div style={{ ...getNodeStyle('right'), right: `-${NODE_SIZE / 2}px`, top: `calc(50% - ${NODE_SIZE / 2}px)` }} onMouseDown={(e) => handleLocalNodeMouseDown(e, 'right', nodePositions.right)} />
        </>
      )}

      {(element.type === 'box' || element.type === 'diamond') && hoveredElementId === element.id && element.width && element.height && onExtensionPointClick && activeTool === 'pointer' && (
        <>
          {/* Top Extension Point */}
          <div
            style={{
              position: 'absolute',
              top: `-${EXTENSION_POINT_OFFSET}px`,
              left: `calc(50% - ${EXTENSION_POINT_SIZE / 2}px)`,
              width: `${EXTENSION_POINT_SIZE}px`,
              height: `${EXTENSION_POINT_SIZE}px`,
              backgroundColor: 'var(--creative-blue)',
              borderRadius: '50%',
              cursor: 'pointer',
              zIndex: 15,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              border: '2px solid white',
            }}
            onClick={(e) => handleExtensionPointClick(e, 'top', {
              x: element.x + (element.width || 0) / 2,
              y: element.y - EXTENSION_POINT_OFFSET - 60 // Add spacing for the new box
            })}
            onMouseEnter={() => onExtensionPointMouseEnter && onExtensionPointMouseEnter(element.id, 'top', {
              x: element.x + (element.width || 0) / 2,
              y: element.y - EXTENSION_POINT_OFFSET - 60 // Add spacing for the new box
            })}
            onMouseLeave={() => onExtensionPointMouseLeave && onExtensionPointMouseLeave()}
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14m-7-7h14" stroke="white" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>

          {/* Bottom Extension Point */}
          <div
            style={{
              position: 'absolute',
              bottom: `-${EXTENSION_POINT_OFFSET}px`,
              left: `calc(50% - ${EXTENSION_POINT_SIZE / 2}px)`,
              width: `${EXTENSION_POINT_SIZE}px`,
              height: `${EXTENSION_POINT_SIZE}px`,
              backgroundColor: 'var(--creative-blue)',
              borderRadius: '50%',
              cursor: 'pointer',
              zIndex: 15,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              border: '2px solid white',
            }}
            onClick={(e) => handleExtensionPointClick(e, 'bottom', {
              x: element.x + (element.width || 0) / 2,
              y: element.y + (element.height || 0) + EXTENSION_POINT_OFFSET + 60 // Add spacing for the new box
            })}
            onMouseEnter={() => onExtensionPointMouseEnter && onExtensionPointMouseEnter(element.id, 'bottom', {
              x: element.x + (element.width || 0) / 2,
              y: element.y + (element.height || 0) + EXTENSION_POINT_OFFSET + 60 // Add spacing for the new box
            })}
            onMouseLeave={() => onExtensionPointMouseLeave && onExtensionPointMouseLeave()}
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14m-7-7h14" stroke="white" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>

          {/* Left Extension Point */}
          <div
            style={{
              position: 'absolute',
              left: `-${EXTENSION_POINT_OFFSET}px`,
              top: `calc(50% - ${EXTENSION_POINT_SIZE / 2}px)`,
              width: `${EXTENSION_POINT_SIZE}px`,
              height: `${EXTENSION_POINT_SIZE}px`,
              backgroundColor: 'var(--creative-blue)',
              borderRadius: '50%',
              cursor: 'pointer',
              zIndex: 15,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              border: '2px solid white',
            }}
            onClick={(e) => handleExtensionPointClick(e, 'left', {
              x: element.x - EXTENSION_POINT_OFFSET - 60, // Add spacing for the new box
              y: element.y + (element.height || 0) / 2
            })}
            onMouseEnter={() => onExtensionPointMouseEnter && onExtensionPointMouseEnter(element.id, 'left', {
              x: element.x - EXTENSION_POINT_OFFSET - 60, // Add spacing for the new box
              y: element.y + (element.height || 0) / 2
            })}
            onMouseLeave={() => onExtensionPointMouseLeave && onExtensionPointMouseLeave()}
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14m-7-7h14" stroke="white" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>

          {/* Right Extension Point */}
          <div
            style={{
              position: 'absolute',
              right: `-${EXTENSION_POINT_OFFSET}px`,
              top: `calc(50% - ${EXTENSION_POINT_SIZE / 2}px)`,
              width: `${EXTENSION_POINT_SIZE}px`,
              height: `${EXTENSION_POINT_SIZE}px`,
              backgroundColor: 'var(--creative-blue)',
              borderRadius: '50%',
              cursor: 'pointer',
              zIndex: 15,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              border: '2px solid white',
            }}
            onClick={(e) => handleExtensionPointClick(e, 'right', {
              x: element.x + (element.width || 0) + EXTENSION_POINT_OFFSET + 60, // Add spacing for the new box
              y: element.y + (element.height || 0) / 2
            })}
            onMouseEnter={() => onExtensionPointMouseEnter && onExtensionPointMouseEnter(element.id, 'right', {
              x: element.x + (element.width || 0) + EXTENSION_POINT_OFFSET + 60, // Add spacing for the new box
              y: element.y + (element.height || 0) / 2
            })}
            onMouseLeave={() => onExtensionPointMouseLeave && onExtensionPointMouseLeave()}
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14m-7-7h14" stroke="white" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>
        </>
      )}

      {resizeHandles.map(handle => (
        <div
          key={handle.type}
          style={handle.style}
          onMouseDown={(e) => handleLocalResizeHandleMouseDown(e, handle.type)}
        />
      ))}
    </div>
  );
};