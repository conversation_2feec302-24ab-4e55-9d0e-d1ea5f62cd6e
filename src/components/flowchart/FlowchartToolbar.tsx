import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Type, Minus, Square, ArrowR<PERSON>, Penci<PERSON>, Undo, Redo, Diamond } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useHistoryContext } from '@/contexts/HistoryContext';

const tools = [
  { name: 'Pointer', icon: <MousePointer size={20} />, id: 'pointer' },
  { name: 'Hand', icon: <Hand size={20} />, id: 'hand' },
  { name: 'Text', icon: <Type size={20} />, id: 'text' },
  { name: 'Box', icon: <Square size={20} />, id: 'box' },
  { name: 'Diamond', icon: <Diamond size={20} />, id: 'diamond' },
  { name: 'Pencil', icon: <Pencil size={20} />, id: 'pencil' },
  { name: 'Line', icon: <Minus size={20} transform="rotate(45)" />, id: 'line' },
  { name: 'Arrow', icon: <ArrowRight size={20} />, id: 'arrow' },
  { name: 'Horizontal Line', icon: <Minus size={20} />, id: 'horizontal-line' },
  { name: 'Vertical Line', icon: <Minus size={20} transform="rotate(90)" />, id: 'vertical-line' },
];

interface FlowchartToolbarProps {
  activeTool: string;
  setActiveTool: (toolId: string) => void;
}

export const FlowchartToolbar: React.FC<FlowchartToolbarProps> = ({ activeTool, setActiveTool }) => {
  const { undo, redo, canUndo, canRedo } = useHistoryContext();

  return (
      <div className="w-16 bg-side-toolbar border-r border-divider-lines p-2 flex flex-col items-center space-y-2">
        {/* Undo/Redo Controls */}
        <div className="flex flex-col space-y-1 mb-2 border-b border-divider-lines pb-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="w-12 h-8 hover:bg-creative-blue-lighter-bg hover:text-creative-blue"
                onClick={undo}
                disabled={!canUndo}
              >
                <Undo size={16} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Undo (Ctrl+Z)</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="w-12 h-8 hover:bg-creative-blue-lighter-bg hover:text-creative-blue"
                onClick={redo}
                disabled={!canRedo}
              >
                <Redo size={16} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Redo (Ctrl+Y)</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Drawing Tools */}
        {tools.map((tool) => (
          <Tooltip key={tool.id}>
            <TooltipTrigger asChild>
              <Button
                variant={activeTool === tool.id ? "default" : "ghost"}
                size="icon"
                className={cn(
                  "w-12 h-12",
                  activeTool === tool.id
                    ? "bg-creative-blue text-white"
                    : "hover:bg-creative-blue-lighter-bg hover:text-creative-blue"
                )}
                onClick={() => setActiveTool(tool.id)}
              >
                {tool.icon}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>{tool.name}</p>
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
  );
};