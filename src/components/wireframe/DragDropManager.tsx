import React, { useState, useCallback, useRef } from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';
import { GridOverlay, snapToGrid, snapRectToGrid } from './GridOverlay';
import { DropZoneIndicator } from './DropZoneIndicator';
import { PositionIndicator } from './PositionIndicator';
import { DragPreview } from './DragPreview';

interface DragDropState {
  isDragging: boolean;
  draggedComponent: WireframeComponent | null;
  dropZone: {
    x: number;
    y: number;
    width: number;
    height: number;
    visible: boolean;
    type: 'valid' | 'invalid' | 'preview';
  };
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
    visible: boolean;
  };
  gridSnapping: boolean;
  showGrid: boolean;
}

interface DragDropManagerProps {
  children: React.ReactNode;
  canvasWidth: number;
  canvasHeight: number;
  zoom: number;
  gridSize?: number;
  onComponentMove?: (componentId: string, x: number, y: number) => void;
  onComponentAdd?: (component: Partial<WireframeComponent>) => void;
}

export const DragDropManager: React.FC<DragDropManagerProps> = ({
  children,
  canvasWidth,
  canvasHeight,
  zoom,
  gridSize = 20,
  onComponentMove,
  onComponentAdd
}) => {
  const [dragState, setDragState] = useState<DragDropState>({
    isDragging: false,
    draggedComponent: null,
    dropZone: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      visible: false,
      type: 'valid'
    },
    position: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      visible: false
    },
    gridSnapping: true,
    showGrid: true
  });

  const canvasRef = useRef<HTMLDivElement>(null);

  const getMousePosition = useCallback((event: MouseEvent | React.MouseEvent) => {
    if (!canvasRef.current) return { x: 0, y: 0 };
    
    const rect = canvasRef.current.getBoundingClientRect();
    return {
      x: (event.clientX - rect.left) / zoom,
      y: (event.clientY - rect.top) / zoom
    };
  }, [zoom]);

  const startDrag = useCallback((component: WireframeComponent, event: React.MouseEvent) => {
    const mousePos = getMousePosition(event);
    
    setDragState(prev => ({
      ...prev,
      isDragging: true,
      draggedComponent: component,
      showGrid: true
    }));
  }, [getMousePosition]);

  const updateDrag = useCallback((event: MouseEvent) => {
    if (!dragState.isDragging || !dragState.draggedComponent) return;

    const mousePos = getMousePosition(event);
    let targetX = mousePos.x - dragState.draggedComponent.width / 2;
    let targetY = mousePos.y - dragState.draggedComponent.height / 2;

    // Apply grid snapping if enabled
    if (dragState.gridSnapping) {
      const snapped = snapRectToGrid({
        x: targetX,
        y: targetY,
        width: dragState.draggedComponent.width,
        height: dragState.draggedComponent.height
      }, gridSize);
      targetX = snapped.x;
      targetY = snapped.y;
    }

    // Check if position is valid (within canvas bounds)
    const isValidPosition = 
      targetX >= 0 && 
      targetY >= 0 && 
      targetX + dragState.draggedComponent.width <= canvasWidth &&
      targetY + dragState.draggedComponent.height <= canvasHeight;

    setDragState(prev => ({
      ...prev,
      dropZone: {
        x: targetX,
        y: targetY,
        width: dragState.draggedComponent!.width,
        height: dragState.draggedComponent!.height,
        visible: true,
        type: isValidPosition ? 'valid' : 'invalid'
      },
      position: {
        x: targetX,
        y: targetY,
        width: dragState.draggedComponent!.width,
        height: dragState.draggedComponent!.height,
        visible: true
      }
    }));
  }, [dragState, getMousePosition, gridSize, canvasWidth, canvasHeight]);

  const endDrag = useCallback(() => {
    if (!dragState.isDragging || !dragState.draggedComponent) return;

    // If it's a valid drop zone, update the component position
    if (dragState.dropZone.type === 'valid' && onComponentMove) {
      onComponentMove(
        dragState.draggedComponent.id,
        dragState.dropZone.x,
        dragState.dropZone.y
      );
    }

    setDragState(prev => ({
      ...prev,
      isDragging: false,
      draggedComponent: null,
      dropZone: { ...prev.dropZone, visible: false },
      position: { ...prev.position, visible: false },
      showGrid: false
    }));
  }, [dragState, onComponentMove]);

  const toggleGridSnapping = useCallback(() => {
    setDragState(prev => ({
      ...prev,
      gridSnapping: !prev.gridSnapping
    }));
  }, []);

  const toggleGrid = useCallback(() => {
    setDragState(prev => ({
      ...prev,
      showGrid: !prev.showGrid
    }));
  }, []);

  // Add event listeners for drag operations
  React.useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', updateDrag);
      document.addEventListener('mouseup', endDrag);
      
      return () => {
        document.removeEventListener('mousemove', updateDrag);
        document.removeEventListener('mouseup', endDrag);
      };
    }
  }, [dragState.isDragging, updateDrag, endDrag]);

  return (
    <div 
      ref={canvasRef}
      className="relative w-full h-full"
      style={{ cursor: dragState.isDragging ? 'grabbing' : 'default' }}
    >
      {/* Grid Overlay */}
      <GridOverlay
        gridSize={gridSize}
        visible={dragState.showGrid}
        zoom={zoom}
        canvasWidth={canvasWidth}
        canvasHeight={canvasHeight}
      />

      {/* Main Content */}
      <div className="relative w-full h-full">
        {children}
      </div>

      {/* Drop Zone Indicator */}
      <DropZoneIndicator
        x={dragState.dropZone.x}
        y={dragState.dropZone.y}
        width={dragState.dropZone.width}
        height={dragState.dropZone.height}
        visible={dragState.dropZone.visible}
        type={dragState.dropZone.type}
        zoom={zoom}
      />

      {/* Position Indicator */}
      <PositionIndicator
        x={dragState.position.x}
        y={dragState.position.y}
        width={dragState.position.width}
        height={dragState.position.height}
        visible={dragState.position.visible}
        canvasWidth={canvasWidth}
        canvasHeight={canvasHeight}
        zoom={zoom}
      />

      {/* Drag Preview */}
      {dragState.draggedComponent && (
        <DragPreview
          component={dragState.draggedComponent}
          isDragging={dragState.isDragging}
        />
      )}

      {/* Controls */}
      <div className="absolute top-4 right-4 flex gap-2 z-50">
        <button
          onClick={toggleGrid}
          className={`px-3 py-1 text-xs rounded ${
            dragState.showGrid 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-200 text-gray-700'
          }`}
        >
          Grid
        </button>
        <button
          onClick={toggleGridSnapping}
          className={`px-3 py-1 text-xs rounded ${
            dragState.gridSnapping 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-200 text-gray-700'
          }`}
        >
          Snap
        </button>
      </div>
    </div>
  );
};

export { snapToGrid, snapRectToGrid };
