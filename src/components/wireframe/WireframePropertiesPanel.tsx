import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Trash2, Palette, Type, MoveUp, MoveDown, ArrowUp, ArrowDown } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';

interface WireframePropertiesPanelProps {
  selectedComponent: WireframeComponent | null;
  onUpdateComponent: (updatedComponent: WireframeComponent) => void;
  onDeleteComponent: (componentId: string) => void;
  onEditData: (componentId: string) => void; // New prop for editing data
  onBringToFront?: (componentId: string) => void;
  onSendToBack?: (componentId: string) => void;
  onBringForward?: (componentId: string) => void;
  onSendBackward?: (componentId: string) => void;
}

export interface WireframeComponent {
  id: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  data?: Record<string, any>; // New data field
  zIndex?: number; // For layering components
  // Enhanced styling properties
  fontSize?: number;
  textColor?: string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  opacity?: number;
  // Icon support
  icon?: string;
  iconPosition?: 'left' | 'right' | 'top' | 'bottom';
  // Auto-sizing
  autoSize?: boolean;
  // Link functionality
  linkType?: 'none' | 'url' | 'page' | 'popup' | 'back';
  linkTarget?: string;
  linkBehavior?: 'same-window' | 'new-window' | 'overlay';
  // Component state
  componentState?: 'normal' | 'hover' | 'active' | 'disabled' | 'selected';
  stateStyles?: {
    normal?: Partial<WireframeComponent>;
    hover?: Partial<WireframeComponent>;
    active?: Partial<WireframeComponent>;
    disabled?: Partial<WireframeComponent>;
    selected?: Partial<WireframeComponent>;
  };
  // Callback for component updates (e.g., image uploads)
  onUpdate?: (component: WireframeComponent) => void;
}

const FONT_SIZES = [
    { label: '0', value: 0 },
    { label: 'XS', value: 10 },
    { label: 'S', value: 12 },
    { label: 'M', value: 16 },
    { label: 'L', value: 20 },
    { label: 'XL', value: 24 },
    { label: 'XXL', value: 32 },
];

const COLOR_PRESETS = [
  '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
  '#808080', '#C0C0C0', '#800000', '#008000', '#000080', '#808000', '#800080', '#008080',
  '#FFA500', '#FFC0CB', '#A52A2A', '#DDA0DD', '#98FB98', '#F0E68C', '#DEB887', '#D2691E'
];

const BORDER_WIDTHS = [
  { label: 'None', value: 0 },
  { label: 'Thin', value: 1 },
  { label: 'Medium', value: 2 },
  { label: 'Thick', value: 4 },
  { label: 'Extra', value: 6 },
];

const BORDER_RADIUS = [
  { label: '0', value: 0 },
  { label: '2', value: 2 },
  { label: '4', value: 4 },
  { label: '6', value: 6 },
  { label: '8', value: 8 },
  { label: '12', value: 12 },
  { label: '16', value: 16 },
  { label: '20', value: 20 },
  { label: '24', value: 24 },
  { label: '∞', value: 9999 } // For fully rounded
];

// Categorized icons for better organization
const ICON_CATEGORIES = {
  'Actions': [
    { label: 'None', value: '' },
    { label: '➕ Add', value: '➕' },
    { label: '✏️ Edit', value: '✏️' },
    { label: '🗑️ Delete', value: '🗑️' },
    { label: '💾 Save', value: '💾' },
    { label: '📤 Upload', value: '📤' },
    { label: '📥 Download', value: '📥' },
    { label: '🔍 Search', value: '🔍' },
    { label: '⚙️ Settings', value: '⚙️' },
    { label: '🔄 Refresh', value: '🔄' },
    { label: '📋 Copy', value: '📋' },
    { label: '✂️ Cut', value: '✂️' },
    { label: '📌 Pin', value: '📌' },
    { label: '🔗 Link', value: '🔗' },
  ],
  'Navigation': [
    { label: '🏠 Home', value: '🏠' },
    { label: '⬅️ Back', value: '⬅️' },
    { label: '➡️ Forward', value: '➡️' },
    { label: '⬆️ Up', value: '⬆️' },
    { label: '⬇️ Down', value: '⬇️' },
    { label: '🔙 Return', value: '🔙' },
    { label: '🔚 End', value: '🔚' },
    { label: '🔛 On', value: '🔛' },
    { label: '🔜 Soon', value: '🔜' },
    { label: '🔝 Top', value: '🔝' },
  ],
  'Communication': [
    { label: '📧 Email', value: '📧' },
    { label: '📞 Phone', value: '📞' },
    { label: '💬 Chat', value: '💬' },
    { label: '📱 Mobile', value: '📱' },
    { label: '📠 Fax', value: '📠' },
    { label: '📢 Announce', value: '📢' },
    { label: '📣 Megaphone', value: '📣' },
    { label: '🔔 Bell', value: '🔔' },
    { label: '🔕 Mute', value: '🔕' },
    { label: '📻 Radio', value: '📻' },
  ],
  'Status': [
    { label: '✅ Check', value: '✅' },
    { label: '❌ Close', value: '❌' },
    { label: '⚠️ Warning', value: '⚠️' },
    { label: '❗ Error', value: '❗' },
    { label: 'ℹ️ Info', value: 'ℹ️' },
    { label: '❓ Question', value: '❓' },
    { label: '💡 Idea', value: '💡' },
    { label: '🔒 Lock', value: '🔒' },
    { label: '🔓 Unlock', value: '🔓' },
    { label: '👁️ View', value: '👁️' },
  ],
  'People': [
    { label: '👤 User', value: '👤' },
    { label: '👥 Users', value: '👥' },
    { label: '👨‍💼 Admin', value: '👨‍💼' },
    { label: '👩‍💻 Developer', value: '👩‍💻' },
    { label: '🧑‍🤝‍🧑 Team', value: '🧑‍🤝‍🧑' },
    { label: '👑 Crown', value: '👑' },
    { label: '🎭 Mask', value: '🎭' },
    { label: '👶 Baby', value: '👶' },
    { label: '👴 Elder', value: '👴' },
    { label: '🤖 Robot', value: '🤖' },
  ],
  'Objects': [
    { label: '⭐ Star', value: '⭐' },
    { label: '❤️ Heart', value: '❤️' },
    { label: '📊 Chart', value: '📊' },
    { label: '🎯 Target', value: '🎯' },
    { label: '🚀 Rocket', value: '🚀' },
    { label: '🔑 Key', value: '🔑' },
    { label: '🏆 Trophy', value: '🏆' },
    { label: '🎁 Gift', value: '🎁' },
    { label: '💎 Diamond', value: '💎' },
    { label: '🔥 Fire', value: '🔥' },
  ]
};

// Flatten all icons for search
const ALL_ICONS = Object.values(ICON_CATEGORIES).flat();

const ICON_POSITIONS = [
  { label: 'Left', value: 'left' },
  { label: 'Right', value: 'right' },
  { label: 'Top', value: 'top' },
  { label: 'Bottom', value: 'bottom' },
];

// Size presets for components
const SIZE_PRESETS = [
  { label: 'XS', width: 60, height: 24 },
  { label: 'S', width: 80, height: 32 },
  { label: 'M', width: 120, height: 40 },
  { label: 'L', width: 160, height: 48 },
  { label: 'XL', width: 200, height: 56 },
  { label: 'XXL', width: 240, height: 64 },
];

// Link types and behaviors
const LINK_TYPES = [
  { label: 'No Link', value: 'none' },
  { label: 'Web URL', value: 'url' },
  { label: 'Page/Screen', value: 'page' },
  { label: 'Popup/Modal', value: 'popup' },
  { label: 'Go Back', value: 'back' },
];

const LINK_BEHAVIORS = [
  { label: 'Same Window', value: 'same-window' },
  { label: 'New Window', value: 'new-window' },
  { label: 'Overlay', value: 'overlay' },
];

// Component states
const COMPONENT_STATES = [
  { label: 'Normal', value: 'normal', icon: '⚪' },
  { label: 'Hover', value: 'hover', icon: '🔵' },
  { label: 'Active', value: 'active', icon: '🟢' },
  { label: 'Disabled', value: 'disabled', icon: '⚫' },
  { label: 'Selected', value: 'selected', icon: '🟡' },
];

// Color Picker Component
const ColorPicker: React.FC<{
  label: string;
  value: string;
  onChange: (color: string) => void;
  showPresets?: boolean;
}> = ({ label, value, onChange, showPresets = true }) => {
  const [showPicker, setShowPicker] = useState(false);

  return (
    <div className="space-y-2">
      <Label className="text-xs font-medium text-gray-600 flex items-center gap-2">
        <Palette size={12} />
        {label}
      </Label>
      <div className="flex items-center gap-2">
        <div
          className="w-6 h-6 rounded border border-gray-300 cursor-pointer flex-shrink-0"
          style={{ backgroundColor: value }}
          onClick={() => setShowPicker(!showPicker)}
        />
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 h-6 text-xs"
          placeholder="#000000"
        />
        <Input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-6 h-6 p-0 border-0 cursor-pointer"
        />
      </div>
      {showPresets && (
        <div className="grid grid-cols-8 gap-1">
          {COLOR_PRESETS.map((color) => (
            <div
              key={color}
              className="w-4 h-4 rounded cursor-pointer border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: color }}
              onClick={() => onChange(color)}
              title={color}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const WireframePropertiesPanel: React.FC<WireframePropertiesPanelProps> = ({
  selectedComponent,
  onUpdateComponent,
  onDeleteComponent,
  onEditData,
  onBringToFront,
  onSendToBack,
  onBringForward,
  onSendBackward
}) => {
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconCategory, setSelectedIconCategory] = useState('Actions');

  const handleChange = (field: keyof WireframeComponent, value: string | number) => {
    if (selectedComponent) {
      const updatedComponent = { ...selectedComponent, [field]: value };
      onUpdateComponent(updatedComponent);
    }
  };

  const handleNumericInput = (field: keyof WireframeComponent, value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      handleChange(field, numValue);
    }
  };

  return (
    <div className="h-full w-64 bg-panel-background border-l border-divider-lines p-4 flex flex-col">
      <h3 className="text-lg font-semibold mb-4 text-gray-700">Properties</h3>
      {selectedComponent ? (
        <div className="text-sm text-gray-500 flex-1 overflow-auto space-y-4">

          {/* Component Type Header */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 capitalize">{selectedComponent.type}</h4>
            <Separator className="mt-1" />
          </div>

          {/* Text Content */}
          <div>
            <label className="block text-xs font-medium text-gray-600">Text</label>
            <textarea
              value={selectedComponent.text || ''}
              onChange={(e) => handleChange('text', e.target.value)}
              className="mt-1 w-full h-16 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Enter component text..."
              style={{ fontFamily: 'inherit' }}
            />
          </div>

          {/* Icon Selection - Show for buttons and other components that support icons */}
          {(selectedComponent.type === 'Button' || selectedComponent.type === 'Search Box') && (
            <div>
              <Label className="text-xs font-medium text-gray-600">Icon</Label>
              <div className="space-y-2 mt-2">
                {/* Icon Search */}
                <Input
                  type="text"
                  value={iconSearchTerm}
                  onChange={(e) => setIconSearchTerm(e.target.value)}
                  className="h-6 text-xs"
                  placeholder="Search icons..."
                />

                {/* Icon Categories */}
                {!iconSearchTerm && (
                  <div className="flex flex-wrap gap-1">
                    {Object.keys(ICON_CATEGORIES).map(category => (
                      <Button
                        key={category}
                        variant={selectedIconCategory === category ? "default" : "outline"}
                        size="sm"
                        className="text-xs px-2 py-1 h-6"
                        onClick={() => setSelectedIconCategory(category)}
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                )}

                {/* Icon Picker */}
                <div className="grid grid-cols-6 gap-1 max-h-32 overflow-y-auto border border-gray-200 rounded p-2">
                  {(iconSearchTerm
                    ? ALL_ICONS.filter(icon =>
                        icon.label.toLowerCase().includes(iconSearchTerm.toLowerCase()) ||
                        icon.value.includes(iconSearchTerm)
                      )
                    : ICON_CATEGORIES[selectedIconCategory as keyof typeof ICON_CATEGORIES] || []
                  ).map((iconOption) => (
                    <button
                      key={iconOption.value}
                      type="button"
                      className={`p-1 text-sm rounded border transition-colors ${
                        selectedComponent.icon === iconOption.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => handleChange('icon', iconOption.value)}
                      title={iconOption.label}
                    >
                      {iconOption.value || '∅'}
                    </button>
                  ))}
                </div>

                {/* Custom Icon Input */}
                <div>
                  <Label className="text-xs text-gray-500">Custom Icon</Label>
                  <Input
                    type="text"
                    value={selectedComponent.icon || ''}
                    onChange={(e) => handleChange('icon', e.target.value)}
                    className="h-6 text-xs"
                    placeholder="Enter emoji or symbol..."
                  />
                </div>

                {/* Icon Position */}
                {selectedComponent.icon && (
                  <div>
                    <Label className="text-xs text-gray-500">Icon Position</Label>
                    <div className="grid grid-cols-4 gap-1 mt-1">
                      {ICON_POSITIONS.map(position => (
                        <Button
                          key={position.value}
                          variant={selectedComponent.iconPosition === position.value ? "default" : "outline"}
                          size="sm"
                          className="text-xs"
                          onClick={() => handleChange('iconPosition', position.value)}
                        >
                          {position.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Font Size */}
          <div>
            <Label className="text-xs font-medium text-gray-600 flex items-center gap-2">
              <Type size={12} />
              Font Size
            </Label>
            <div className="grid grid-cols-3 gap-1 mt-2">
              {FONT_SIZES.map(size => (
                <Button
                  key={size.label}
                  variant={selectedComponent.fontSize === size.value ? "default" : "outline"}
                  size="sm"
                  className="text-xs hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white"
                  onClick={() => handleChange('fontSize', size.value)}
                >
                  {size.label}
                </Button>
              ))}
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Input
                type="number"
                value={selectedComponent.fontSize !== undefined ? selectedComponent.fontSize : ''}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  if (inputValue === '' || inputValue === '0') {
                    handleChange('fontSize', 0);
                  } else {
                    handleChange('fontSize', Math.max(0, Math.min(72, parseInt(inputValue) || 0)));
                  }
                }}
                className="flex-1 h-6 text-xs"
                min="0"
                max="72"
                placeholder="Custom size (0 to clear)"
              />
              <span className="text-xs text-gray-500">px</span>
            </div>
          </div>

          {/* Text Color */}
          <ColorPicker
            label="Text Color"
            value={selectedComponent.textColor || '#000000'}
            onChange={(color) => handleChange('textColor', color)}
          />

          <Separator />

          {/* Background Color */}
          <ColorPicker
            label="Background Color"
            value={selectedComponent.backgroundColor || '#FFFFFF'}
            onChange={(color) => handleChange('backgroundColor', color)}
          />

          {/* Border Color */}
          <ColorPicker
            label="Border Color"
            value={selectedComponent.borderColor || '#000000'}
            onChange={(color) => handleChange('borderColor', color)}
          />

          {/* Border Width */}
          <div>
            <Label className="text-xs font-medium text-gray-600">Border Width</Label>
            <div className="grid grid-cols-5 gap-1 mt-2">
              {BORDER_WIDTHS.map(width => (
                <Button
                  key={width.label}
                  variant={selectedComponent.borderWidth === width.value ? "default" : "outline"}
                  size="sm"
                  className="text-xs hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white"
                  onClick={() => handleChange('borderWidth', width.value)}
                >
                  {width.label}
                </Button>
              ))}
            </div>
            <div className="flex items-center gap-2 mt-2">

          {/* Border Radius */}
          <div>
            <Label className="text-xs font-medium text-gray-600">Border Radius</Label>
            <div className="grid grid-cols-5 gap-1 mt-2">
              {BORDER_RADIUS.map(radius => (
                <Button
                  key={radius.label}
                  variant={selectedComponent.borderRadius === radius.value ? "default" : "outline"}
                  size="sm"
                  className="text-xs hover:bg-creative-blue-lighter-bg hover:text-creative-blue focus:bg-creative-blue focus:text-white"
                  onClick={() => handleChange('borderRadius', radius.value)}
                >
                  {radius.label}
                </Button>
              ))}
            </div>
            <div className="mt-2">
              <Input
                type="number"
                placeholder="Custom px (0 to clear)"
                value={selectedComponent.borderRadius !== undefined ? selectedComponent.borderRadius : ''}
                onChange={(e) => {
                  const inputValue = e.target.value;
                  if (inputValue === '' || inputValue === '0') {
                    handleChange('borderRadius', 0);
                  } else {
                    handleChange('borderRadius', Math.max(0, Math.min(100, parseInt(inputValue) || 0)));
                  }
                }}
                className="text-xs h-8"
                min="0"
                max="100"
              />
            </div>
          </div>
              <Input
                type="number"
                value={selectedComponent.borderWidth || 1}
                onChange={(e) => handleNumericInput('borderWidth', e.target.value)}
                className="flex-1 h-6 text-xs"
                min="0"
                max="20"
                placeholder="Custom width"
              />
              <span className="text-xs text-gray-500">px</span>
            </div>
          </div>

          <Separator />

          {/* Component State */}
          <div>
            <Label className="text-xs font-medium text-gray-600">Component State</Label>
            <div className="space-y-2 mt-2">
              {/* State Selector */}
              <div className="grid grid-cols-5 gap-1">
                {COMPONENT_STATES.map(state => (
                  <Button
                    key={state.value}
                    variant={selectedComponent.componentState === state.value ? "default" : "outline"}
                    size="sm"
                    className="text-xs p-1 flex flex-col items-center"
                    onClick={() => handleChange('componentState', state.value)}
                    title={state.label}
                  >
                    <span className="text-xs">{state.icon}</span>
                    <span className="text-xs leading-none">{state.label.slice(0, 3)}</span>
                  </Button>
                ))}
              </div>

              {/* State Info */}
              <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                <strong>Current State:</strong> {
                  COMPONENT_STATES.find(s => s.value === (selectedComponent.componentState || 'normal'))?.label || 'Normal'
                }
                <br />
                <span className="text-xs">💡 Different states can have different colors and styles</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Position and Size */}
          <div className="space-y-3">
            <div>
              <Label className="text-xs font-medium text-gray-600">Position</Label>
              <div className="grid grid-cols-2 gap-2 mt-1">
                <div>
                  <Label className="text-xs text-gray-500">X</Label>
                  <Input
                    type="number"
                    value={Math.round(selectedComponent.x)}
                    onChange={(e) => handleNumericInput('x', e.target.value)}
                    className="h-6 text-xs"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-500">Y</Label>
                  <Input
                    type="number"
                    value={Math.round(selectedComponent.y)}
                    onChange={(e) => handleNumericInput('y', e.target.value)}
                    className="h-6 text-xs"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label className="text-xs font-medium text-gray-600">Size</Label>
              <div className="grid grid-cols-2 gap-2 mt-1">
                <div>
                  <Label className="text-xs text-gray-500">Width</Label>
                  <Input
                    type="number"
                    value={Math.round(selectedComponent.width)}
                    onChange={(e) => handleNumericInput('width', e.target.value)}
                    className="h-6 text-xs"
                    min="10"
                    disabled={selectedComponent.autoSize}
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-500">Height</Label>
                  <Input
                    type="number"
                    value={Math.round(selectedComponent.height)}
                    onChange={(e) => handleNumericInput('height', e.target.value)}
                    className="h-6 text-xs"
                    min="10"
                    disabled={selectedComponent.autoSize}
                  />
                </div>
              </div>

              {/* Size Presets */}
              <div className="mt-2">
                <Label className="text-xs text-gray-500">Size Presets</Label>
                <div className="grid grid-cols-6 gap-1 mt-1">
                  {SIZE_PRESETS.map(preset => (
                    <Button
                      key={preset.label}
                      variant="outline"
                      size="sm"
                      className="text-xs px-1 py-1 h-6"
                      onClick={() => {
                        handleChange('width', preset.width);
                        handleChange('height', preset.height);
                      }}
                      disabled={selectedComponent.autoSize}
                      title={`${preset.width}×${preset.height}`}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Auto-Size Toggle */}
              <div className="mt-2">
                <Button
                  variant={selectedComponent.autoSize ? "default" : "outline"}
                  size="sm"
                  className="w-full text-xs"
                  onClick={() => handleChange('autoSize', !selectedComponent.autoSize)}
                >
                  📐 Auto-Size
                </Button>
              </div>
            </div>
          </div>

          <Separator />

          {/* Layering Controls */}
          <div>
            <Label className="text-xs font-medium text-gray-600">Layering</Label>
            <div className="grid grid-cols-4 gap-1 mt-2">
              <Button
                variant="outline"
                size="sm"
                className="text-xs p-1"
                onClick={() => onBringToFront && selectedComponent && onBringToFront(selectedComponent.id)}
                title="Bring to Front"
              >
                <MoveUp className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-xs p-1"
                onClick={() => onBringForward && selectedComponent && onBringForward(selectedComponent.id)}
                title="Bring Forward"
              >
                <ArrowUp className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-xs p-1"
                onClick={() => onSendBackward && selectedComponent && onSendBackward(selectedComponent.id)}
                title="Send Backward"
              >
                <ArrowDown className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-xs p-1"
                onClick={() => onSendToBack && selectedComponent && onSendToBack(selectedComponent.id)}
                title="Send to Back"
              >
                <MoveDown className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <Separator />

          {/* Links */}
          <div>
            <Label className="text-xs font-medium text-gray-600">Links</Label>
            <div className="space-y-2 mt-2">
              {/* Link Type */}
              <div>
                <Label className="text-xs text-gray-500">Link Type</Label>
                <select
                  value={selectedComponent.linkType || 'none'}
                  onChange={(e) => handleChange('linkType', e.target.value)}
                  className="w-full h-6 text-xs border border-gray-300 rounded px-2"
                >
                  {LINK_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Link Target - Show when not 'none' or 'back' */}
              {selectedComponent.linkType && selectedComponent.linkType !== 'none' && selectedComponent.linkType !== 'back' && (
                <div>
                  <Label className="text-xs text-gray-500">
                    {selectedComponent.linkType === 'url' ? 'URL' :
                     selectedComponent.linkType === 'page' ? 'Page/Screen Name' :
                     'Target'}
                  </Label>
                  <Input
                    type="text"
                    value={selectedComponent.linkTarget || ''}
                    onChange={(e) => handleChange('linkTarget', e.target.value)}
                    className="h-6 text-xs"
                    placeholder={
                      selectedComponent.linkType === 'url' ? 'https://example.com' :
                      selectedComponent.linkType === 'page' ? 'Page name' :
                      'Target name'
                    }
                  />
                </div>
              )}

              {/* Link Behavior - Show when link type is set */}
              {selectedComponent.linkType && selectedComponent.linkType !== 'none' && (
                <div>
                  <Label className="text-xs text-gray-500">Behavior</Label>
                  <select
                    value={selectedComponent.linkBehavior || 'same-window'}
                    onChange={(e) => handleChange('linkBehavior', e.target.value)}
                    className="w-full h-6 text-xs border border-gray-300 rounded px-2"
                  >
                    {LINK_BEHAVIORS.map(behavior => (
                      <option key={behavior.value} value={behavior.value}>
                        {behavior.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Link Preview */}
              {selectedComponent.linkType && selectedComponent.linkType !== 'none' && (
                <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                  <strong>Link:</strong> {
                    selectedComponent.linkType === 'back' ? 'Go back to previous page' :
                    selectedComponent.linkType === 'url' ? selectedComponent.linkTarget || 'No URL specified' :
                    selectedComponent.linkType === 'page' ? `Navigate to "${selectedComponent.linkTarget || 'No page specified'}"` :
                    selectedComponent.linkType === 'popup' ? `Show popup "${selectedComponent.linkTarget || 'No popup specified'}"` :
                    'No link configured'
                  }
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Image-specific controls */}
          {selectedComponent.type === 'Image' && (
            <div className="space-y-3">
              <Label className="text-xs font-medium text-gray-600">Image</Label>

              {selectedComponent.data?.imageUrl ? (
                <div className="space-y-2">
                  <div className="text-xs text-gray-500">Current Image:</div>
                  <div
                    className="w-full h-20 border border-gray-200 rounded overflow-hidden bg-gray-50 flex items-center justify-center"
                  >
                    <img
                      src={selectedComponent.data.imageUrl}
                      alt="Preview"
                      className="max-w-full max-h-full object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement!.innerHTML = '<div class="text-xs text-red-500">Failed to load</div>';
                      }}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (event) => {
                              const imageUrl = event.target?.result as string;
                              handleChange('data', {
                                ...selectedComponent.data,
                                imageUrl,
                              });
                            };
                            reader.readAsDataURL(file);
                          }
                        };
                        input.click();
                      }}
                    >
                      📁 Upload
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => {
                        const url = prompt('Enter image URL:', selectedComponent.data?.imageUrl || '');
                        if (url !== null) {
                          handleChange('data', {
                            ...selectedComponent.data,
                            imageUrl: url || undefined
                          });
                        }
                      }}
                    >
                      🔗 URL
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => {
                      handleChange('data', {
                        ...selectedComponent.data,
                        imageUrl: undefined
                      });
                    }}
                  >
                    Remove Image
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="text-xs text-gray-500 text-center py-4 border border-dashed border-gray-300 rounded">
                    No image selected
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (event) => {
                              const imageUrl = event.target?.result as string;
                              handleChange('data', {
                                ...selectedComponent.data,
                                imageUrl,
                              });
                            };
                            reader.readAsDataURL(file);
                          }
                        };
                        input.click();
                      }}
                    >
                      📁 Upload Image
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => {
                        const url = prompt('Enter image URL:');
                        if (url) {
                          handleChange('data', {
                            ...selectedComponent.data,
                            imageUrl: url
                          });
                        }
                      }}
                    >
                      🔗 Add URL
                    </Button>
                  </div>
                </div>
              )}

              <Separator />
            </div>
          )}

          {/* Data and Actions */}
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => selectedComponent && onEditData(selectedComponent.id)}
            >
              Edit Data
            </Button>

            <Button
              variant="destructive"
              size="sm"
              className="w-full"
              onClick={() => selectedComponent && onDeleteComponent(selectedComponent.id)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Component
            </Button>
          </div>
        </div>
      ) : (
        <p className="text-center text-gray-500 mt-8">Select a component on the canvas to see its properties.</p>
      )}
    </div>
  );
};