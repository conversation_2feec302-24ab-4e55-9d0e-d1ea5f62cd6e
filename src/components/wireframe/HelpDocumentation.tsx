import React, { useState } from 'react';

interface HelpDocumentationProps {
  visible: boolean;
  onClose: () => void;
}

export const HelpDocumentation: React.FC<HelpDocumentationProps> = ({ visible, onClose }) => {
  const [activeSection, setActiveSection] = useState('getting-started');

  if (!visible) return null;

  const sections = [
    { id: 'getting-started', title: 'Getting Started', icon: '🚀' },
    { id: 'components', title: 'Components', icon: '🧩' },
    { id: 'drag-drop', title: 'Drag & Drop', icon: '🖱️' },
    { id: 'grid-snapping', title: 'Grid & Snapping', icon: '📐' },
    { id: 'keyboard', title: 'Keyboard Shortcuts', icon: '⌨️' },
    { id: 'import-export', title: 'Import/Export', icon: '🔄' },
    { id: 'tips', title: 'Tips & Tricks', icon: '💡' }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'getting-started':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Welcome to the Wireframe Tool</h3>
            <p>This powerful wireframing tool helps you create professional wireframes quickly and efficiently.</p>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Quick Start:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Create or select a page from the left panel</li>
                <li>Choose components from the toolbar</li>
                <li>Click to add components to your wireframe</li>
                <li>Drag components to reposition them</li>
                <li>Double-click components to edit their properties</li>
              </ol>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Key Features:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>39 professional UI components across 7 categories</li>
                <li>Grid snapping for precise alignment</li>
                <li>Real-time visual feedback during drag operations</li>
                <li>Import/export between flowcharts and wireframes</li>
                <li>Keyboard shortcuts for efficient workflow</li>
              </ul>
            </div>
          </div>
        );

      case 'components':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Component Library</h3>
            <p>Choose from 39 professional components organized into categories:</p>
            
            <div className="grid grid-cols-1 gap-3">
              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-blue-600">Layouts (5 components)</h4>
                <p className="text-sm text-gray-600">Desktop Browser, Android Browser, iOS Browser, Inspector Panel, Modal</p>
              </div>
              
              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-green-600">Form Controls (10 components)</h4>
                <p className="text-sm text-gray-600">Button, Radio Button, Checkbox, Dropdown, Text Input, Text Area, Slider, Progress Bar, Search Box, Date Picker</p>
              </div>
              
              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-purple-600">Navigation (4 components)</h4>
                <p className="text-sm text-gray-600">Navbar, Sidebar, Pagination, Tabs</p>
              </div>
              
              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-orange-600">Media & Placeholders (5 components)</h4>
                <p className="text-sm text-gray-600">Image Placeholder, Video Placeholder, Icon Placeholder, Avatar, Map Placeholder</p>
              </div>
              
              <div className="border rounded-lg p-3">
                <h4 className="font-medium text-red-600">Data & Lists (5 components)</h4>
                <p className="text-sm text-gray-600">Table, Data Grid, List, Bar Chart, Pie Chart</p>
              </div>
            </div>
          </div>
        );

      case 'drag-drop':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Drag & Drop System</h3>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Visual Feedback:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><span className="text-green-600">Green dashed outline</span> - Valid drop zone</li>
                <li><span className="text-red-600">Red dashed outline</span> - Invalid drop zone</li>
                <li><span className="text-blue-600">Blue coordinates</span> - Real-time position display</li>
                <li><span className="text-red-600">Red center lines</span> - Canvas center alignment guides</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Drag Behavior:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Click and hold to start dragging</li>
                <li>Components become semi-transparent while dragging</li>
                <li>Automatic grid snapping (when enabled)</li>
                <li>Smooth animations and transitions</li>
                <li>Corner handles appear when selected</li>
              </ul>
            </div>
          </div>
        );

      case 'grid-snapping':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Grid & Snapping</h3>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Grid System:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>20px default grid size for consistent spacing</li>
                <li>Grid automatically scales with zoom level</li>
                <li>Toggle visibility with the "Grid" button</li>
                <li>Grid hides when too dense at low zoom levels</li>
              </ul>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Smart Snapping:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Components automatically snap to grid intersections</li>
                <li>Toggle snapping with the "Snap" button</li>
                <li>Works during both placement and movement</li>
                <li>Ensures pixel-perfect alignment</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Alignment Guides:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Red lines appear when components are centered</li>
                <li>Blue guides for edge alignment</li>
                <li>Real-time coordinate display</li>
                <li>Visual feedback for precise positioning</li>
              </ul>
            </div>
          </div>
        );

      case 'keyboard':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Navigation & Selection</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Tab / Shift+Tab</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Navigate components</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Arrow Keys</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Move selected (1px)</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Shift + Arrows</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Move selected (grid)</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Escape</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Clear selection</kbd>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">View Controls</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>G</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Toggle grid</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Shift + S</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Toggle snapping</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Ctrl/Cmd + +</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Zoom in</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Ctrl/Cmd + -</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Zoom out</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Ctrl/Cmd + 0</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Reset zoom</kbd>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'import-export':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Import/Export Features</h3>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Flowchart to Wireframe:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Automatically creates wireframe pages from flowchart boxes</li>
                <li>Uses box text as page names</li>
                <li>Sanitizes names and removes special characters</li>
                <li>Adds starter components to each page</li>
                <li>Preserves project relationships</li>
              </ul>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Wireframe to Flowchart:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Converts wireframe pages to flowchart boxes</li>
                <li>Arranges boxes in a grid layout</li>
                <li>Maintains page names and relationships</li>
                <li>Creates visual flow representation</li>
              </ul>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Usage:</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Create your flowchart or wireframe</li>
                <li>Click "Import to Wireframe" or "Import to Flowchart"</li>
                <li>System automatically navigates to the target tool</li>
                <li>Review and refine the imported content</li>
              </ol>
            </div>
          </div>
        );

      case 'tips':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Tips & Best Practices</h3>
            
            <div className="space-y-3">
              <div className="bg-green-50 p-3 rounded-lg">
                <h4 className="font-medium text-green-800">💡 Pro Tip: Grid Snapping</h4>
                <p className="text-sm text-green-700">Keep grid snapping enabled for consistent layouts. Use Shift+Arrow keys to move components by grid increments.</p>
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="font-medium text-blue-800">🎯 Pro Tip: Component Selection</h4>
                <p className="text-sm text-blue-700">Use Tab to cycle through components quickly. This is especially useful for accessibility and keyboard-only navigation.</p>
              </div>

              <div className="bg-purple-50 p-3 rounded-lg">
                <h4 className="font-medium text-purple-800">🚀 Pro Tip: Workflow</h4>
                <p className="text-sm text-purple-700">Start with layout components (browsers, modals) then add form controls and content. This creates a natural design hierarchy.</p>
              </div>

              <div className="bg-orange-50 p-3 rounded-lg">
                <h4 className="font-medium text-orange-800">⚡ Pro Tip: Performance</h4>
                <p className="text-sm text-orange-700">The tool automatically optimizes rendering for large wireframes. Only visible components are rendered for smooth performance.</p>
              </div>

              <div className="bg-red-50 p-3 rounded-lg">
                <h4 className="font-medium text-red-800">🔄 Pro Tip: Import/Export</h4>
                <p className="text-sm text-red-700">Use meaningful names in your flowchart boxes - they become your wireframe page names automatically.</p>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Select a section to view help content.</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Help & Documentation</h2>
          </div>
          <nav className="space-y-1">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                  activeSection === section.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'hover:bg-gray-100'
                }`}
              >
                <span className="mr-2">{section.icon}</span>
                {section.title}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold">
              {sections.find(s => s.id === activeSection)?.title}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ✕
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};
