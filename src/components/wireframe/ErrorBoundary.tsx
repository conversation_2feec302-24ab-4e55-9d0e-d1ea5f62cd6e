import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class WireframeErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Wireframe Error Boundary caught an error:', error, errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="flex flex-col items-center justify-center h-full p-8 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-red-800 mb-2">
            Something went wrong with the wireframe
          </h2>
          <p className="text-red-600 text-center mb-4 max-w-md">
            An unexpected error occurred while rendering the wireframe components. 
            This might be due to corrupted data or a component rendering issue.
          </p>
          
          <div className="flex gap-2 mb-4">
            <button
              onClick={this.handleReset}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Reload Page
            </button>
          </div>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="w-full max-w-2xl">
              <summary className="cursor-pointer text-red-700 font-medium mb-2">
                Error Details (Development Only)
              </summary>
              <div className="bg-red-100 p-4 rounded border text-sm font-mono overflow-auto">
                <div className="text-red-800 font-bold mb-2">Error:</div>
                <div className="text-red-700 mb-4">{this.state.error.toString()}</div>
                
                {this.state.errorInfo && (
                  <>
                    <div className="text-red-800 font-bold mb-2">Component Stack:</div>
                    <pre className="text-red-700 whitespace-pre-wrap">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
  const WrappedComponent = (props: P) => (
    <WireframeErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </WireframeErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook for error reporting
export const useErrorReporting = () => {
  const reportError = React.useCallback((error: Error, context?: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(`Error in ${context || 'unknown context'}:`, error);
    } else {
      // In production, send to error reporting service
      // errorReportingService.captureException(error, { tags: { context } });
    }
  }, []);

  return { reportError };
};

// Component-specific error boundaries
export const ComponentRendererErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <WireframeErrorBoundary
    fallback={
      <div className="flex items-center justify-center w-full h-full border-2 border-dashed border-red-300 bg-red-50 text-red-600 text-sm">
        Component Error
      </div>
    }
    onError={(error, errorInfo) => {
      console.error('Component rendering error:', error, errorInfo);
    }}
  >
    {children}
  </WireframeErrorBoundary>
);

export const DragDropErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <WireframeErrorBoundary
    fallback={
      <div className="flex items-center justify-center w-full h-full bg-yellow-50 border border-yellow-200 text-yellow-800 text-sm">
        Drag & Drop temporarily unavailable. Please refresh the page.
      </div>
    }
    onError={(error, errorInfo) => {
      console.error('Drag & Drop error:', error, errorInfo);
    }}
  >
    {children}
  </WireframeErrorBoundary>
);
