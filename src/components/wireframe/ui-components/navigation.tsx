import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || 'transparent',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#e5e7eb'}`
        : defaultStyles.border || '1px solid #e5e7eb',
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Navbar
export const Navbar: React.FC<ComponentProps> = ({ component, isSelected }) => {
  // Get navigation items from component data, with fallback to default items
  const defaultNavItems = ['Home', 'About', 'Services', 'Contact'];
  const navItems = component.data?.navItems || defaultNavItems;
  const brandText = component.text || 'Brand';
  const showAvatar = component.data?.showAvatar !== false; // Default to true
  const avatarText = component.data?.avatarText || '👤';

  return (
    <div
      style={getComponentStyle(component, isSelected, {
        backgroundColor: component.backgroundColor || '#ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 16px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      })}
    >
      <div style={{
        fontSize: component.fontSize ? component.fontSize + 4 : 18, // Brand text slightly larger
        fontWeight: '600',
        color: component.textColor || '#111827'
      }}>
        {brandText}
      </div>
      <div style={{
        display: 'flex',
        gap: '24px',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#6b7280'
      }}>
        {Array.isArray(navItems) ? navItems.map((item, index) => (
          <span key={index}>{item}</span>
        )) : (
          // Fallback if navItems is not an array
          defaultNavItems.map((item, index) => (
            <span key={index}>{item}</span>
          ))
        )}
      </div>
      {showAvatar && (
        <div style={{
          width: '32px',
          height: '32px',
          backgroundColor: component.borderColor || '#f3f4f6',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: component.textColor || '#6b7280'
        }}>
          {avatarText}
        </div>
      )}
    </div>
  );
};

// Sidebar
export const Sidebar: React.FC<ComponentProps> = ({ component, isSelected }) => {
  // Get menu items from component data, with fallback to default items
  const defaultMenuItems = ['Dashboard', 'Projects', 'Tasks', 'Reports', 'Settings'];
  const menuItems = component.data?.menuItems || defaultMenuItems;
  const menuTitle = component.text || 'Menu';

  return (
    <div
      style={getComponentStyle(component, isSelected, {
        backgroundColor: component.backgroundColor || '#f9fafb',
        padding: '16px',
        overflow: 'hidden'
      })}
    >
      <div style={{
        fontSize: component.fontSize ? component.fontSize + 2 : 16, // Title slightly larger
        fontWeight: '600',
        color: component.textColor || '#111827',
        marginBottom: '16px'
      }}>
        {menuTitle}
      </div>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px'
      }}>
        {Array.isArray(menuItems) ? menuItems.map((item, index) => (
          <div
            key={index}
            style={{
              padding: '8px 12px',
              borderRadius: '6px',
              backgroundColor: index === 0 ? (component.borderColor || '#3b82f6') : 'transparent',
              color: index === 0 ? '#ffffff' : (component.textColor || '#6b7280'),
              fontSize: component.fontSize || 14,
              cursor: 'pointer'
            }}
          >
            {item}
          </div>
        )) : (
          // Fallback if menuItems is not an array
          defaultMenuItems.map((item, index) => (
            <div
              key={index}
              style={{
                padding: '8px 12px',
                borderRadius: '6px',
                backgroundColor: index === 0 ? (component.borderColor || '#3b82f6') : 'transparent',
                color: index === 0 ? '#ffffff' : (component.textColor || '#6b7280'),
                fontSize: component.fontSize || 14,
                cursor: 'pointer'
              }}
            >
              {item}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Pagination
export const Pagination: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px'
    }}
  >
    <div style={{
      width: '32px',
      height: '32px',
      border: '1px solid #d1d5db',
      borderRadius: '6px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '14px',
      color: '#6b7280',
      cursor: 'pointer'
    }}>
      ‹
    </div>
    {[1, 2, 3, 4, 5].map((page) => (
      <div
        key={page}
        style={{
          width: '32px',
          height: '32px',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px',
          backgroundColor: page === 2 ? '#3b82f6' : '#ffffff',
          color: page === 2 ? '#ffffff' : '#374151',
          cursor: 'pointer'
        }}
      >
        {page}
      </div>
    ))}
    <div style={{
      width: '32px',
      height: '32px',
      border: '1px solid #d1d5db',
      borderRadius: '6px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '14px',
      color: '#6b7280',
      cursor: 'pointer'
    }}>
      ›
    </div>
  </div>
);

// Tabs
export const Tabs: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    <div style={{
      display: 'flex',
      borderBottom: '1px solid #e5e7eb'
    }}>
      {['Tab 1', 'Tab 2', 'Tab 3'].map((tab, index) => (
        <div
          key={index}
          style={{
            padding: '12px 16px',
            fontSize: '14px',
            color: index === 0 ? '#3b82f6' : '#6b7280',
            borderBottom: index === 0 ? '2px solid #3b82f6' : '2px solid transparent',
            cursor: 'pointer',
            fontWeight: index === 0 ? '500' : '400'
          }}
        >
          {tab}
        </div>
      ))}
    </div>
    <div style={{
      flex: 1,
      padding: '16px',
      fontSize: '14px',
      color: '#6b7280',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {component.text || 'Tab Content'}
    </div>
  </div>
);

// App Bar Component
export const AppBar: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 375,
      height: component.height || 60,
      backgroundColor: component.backgroundColor || '#3b82f6',
      color: component.textColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 16px',
      fontSize: component.fontSize || 16,
      fontWeight: '500'
    }}
  >
    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
      <div style={{ fontSize: '18px' }}>←</div>
      <span>{component.text || 'App Title'}</span>
    </div>
    <div style={{ fontSize: '18px' }}>⋮</div>
  </div>
);

// Breadcrumb Component
export const Breadcrumb: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 32,
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    }}
  >
    <span style={{ color: '#3b82f6', cursor: 'pointer' }}>Home</span>
    <span>›</span>
    <span style={{ color: '#3b82f6', cursor: 'pointer' }}>Category</span>
    <span>›</span>
    <span style={{ color: '#374151', fontWeight: '500' }}>
      {component.text || 'Current Page'}
    </span>
  </div>
);

// Link Bar Component
export const LinkBar: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#f9fafb',
      border: `1px solid ${component.borderColor || '#e5e7eb'}`,
      borderRadius: '6px',
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px',
      gap: '24px'
    }}
  >
    {['Home', 'About', 'Services', 'Contact'].map((link, index) => (
      <span
        key={index}
        style={{
          fontSize: component.fontSize || 14,
          color: index === 0 ? '#3b82f6' : component.textColor || '#6b7280',
          cursor: 'pointer',
          textDecoration: index === 0 ? 'underline' : 'none',
          fontWeight: index === 0 ? '500' : '400'
        }}
      >
        {index === 0 && component.text ? component.text : link}
      </span>
    ))}
  </div>
);

// Menu Component
export const Menu: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 160,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      overflow: 'hidden'
    }}
  >
    {['Menu Item 1', 'Menu Item 2', 'Menu Item 3', 'Menu Item 4'].map((item, index) => (
      <div
        key={index}
        style={{
          padding: '12px 16px',
          fontSize: component.fontSize || 14,
          color: component.textColor || '#374151',
          borderBottom: index < 3 ? '1px solid #e5e7eb' : 'none',
          cursor: 'pointer',
          backgroundColor: index === 0 ? '#f3f4f6' : 'transparent'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#f3f4f6';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = index === 0 ? '#f3f4f6' : 'transparent';
        }}
      >
        {index === 0 && component.text ? component.text : item}
      </div>
    ))}
  </div>
);

// Toolbar Component
export const Toolbar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#f3f4f6',
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      display: 'flex',
      alignItems: 'center',
      padding: '0 8px',
      gap: '4px'
    }}
  >
    {['📁', '💾', '✂️', '📋', '↶', '↷', '🔍'].map((icon, index) => (
      <button
        key={index}
        style={{
          width: '32px',
          height: '32px',
          border: '1px solid #d1d5db',
          borderRadius: '4px',
          backgroundColor: '#ffffff',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px'
        }}
      >
        {icon}
      </button>
    ))}
    <div style={{ flex: 1 }} />
    <span style={{
      fontSize: component.fontSize || 12,
      color: component.textColor || '#6b7280'
    }}>
      {component.text || 'Toolbar'}
    </span>
  </div>
);

// Popover Component
export const Popover: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 100,
      position: 'relative'
    }}
  >
    {/* Popover content */}
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: component.backgroundColor || '#ffffff',
        border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
        borderRadius: '8px',
        padding: '12px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151'
      }}
    >
      {component.text || 'Popover content goes here'}
    </div>
    {/* Popover arrow */}
    <div
      style={{
        position: 'absolute',
        bottom: '-8px',
        left: '20px',
        width: '0',
        height: '0',
        borderLeft: '8px solid transparent',
        borderRight: '8px solid transparent',
        borderTop: `8px solid ${component.backgroundColor || '#ffffff'}`
      }}
    />
    <div
      style={{
        position: 'absolute',
        bottom: '-9px',
        left: '20px',
        width: '0',
        height: '0',
        borderLeft: '8px solid transparent',
        borderRight: '8px solid transparent',
        borderTop: `8px solid ${component.borderColor || '#d1d5db'}`
      }}
    />
  </div>
);

// Progress Indicator Component
export const ProgressIndicator: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const progress = component.data?.progress || 60;

  return (
    <div
      style={{
        width: component.width || 300,
        height: component.height || 30,
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        border: isSelected ? '2px solid #3b82f6' : 'none',
        borderRadius: '4px',
        padding: '4px'
      }}
    >
      <div
        style={{
          flex: 1,
          height: '8px',
          backgroundColor: component.backgroundColor || '#e5e7eb',
          borderRadius: '4px',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: '100%',
            backgroundColor: component.borderColor || '#3b82f6',
            borderRadius: '4px',
            transition: 'width 0.3s ease'
          }}
        />
      </div>
      <span
        style={{
          fontSize: component.fontSize || 12,
          color: component.textColor || '#6b7280',
          minWidth: '40px'
        }}
      >
        {component.text || `${progress}%`}
      </span>
    </div>
  );
};

export const navigationComponents = {
  'Navbar': Navbar,
  'Sidebar': Sidebar,
  'Pagination': Pagination,
  'Tabs': Tabs,
  'App Bar': AppBar,
  'Breadcrumb': Breadcrumb,
  'Link Bar': LinkBar,
  'Menu': Menu,
  'Toolbar': Toolbar,
  'Popover': Popover,
  'Progress Indicator': ProgressIndicator
};
