import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || '#ffffff',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#e5e7eb'}`
        : defaultStyles.border || '1px solid #e5e7eb',
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Table
export const Table: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      overflow: 'hidden'
    })}
  >
    {/* Header */}
    <div style={{
      display: 'flex',
      backgroundColor: component.borderColor || '#f9fafb',
      borderBottom: `1px solid ${component.borderColor || '#e5e7eb'}`,
      fontSize: component.fontSize || 12,
      fontWeight: '600',
      color: component.textColor || '#374151'
    }}>
      <div style={{ flex: 1, padding: '8px 12px', borderRight: `1px solid ${component.borderColor || '#e5e7eb'}` }}>Name</div>
      <div style={{ flex: 1, padding: '8px 12px', borderRight: `1px solid ${component.borderColor || '#e5e7eb'}` }}>Email</div>
      <div style={{ flex: 1, padding: '8px 12px' }}>Status</div>
    </div>
    {/* Rows */}
    {[1, 2, 3].map((row) => (
      <div
        key={row}
        style={{
          display: 'flex',
          borderBottom: '1px solid #f3f4f6',
          fontSize: component.fontSize || 12,
          color: component.textColor || '#6b7280'
        }}
      >
        <div style={{ flex: 1, padding: '8px 12px', borderRight: '1px solid #f3f4f6' }}>
          User {row}
        </div>
        <div style={{ flex: 1, padding: '8px 12px', borderRight: '1px solid #f3f4f6' }}>
          user{row}@email.com
        </div>
        <div style={{ flex: 1, padding: '8px 12px' }}>
          <span style={{
            padding: '2px 6px',
            borderRadius: '12px',
            backgroundColor: row === 1 ? '#dcfce7' : '#fef3c7',
            color: row === 1 ? '#166534' : '#92400e',
            fontSize: (component.fontSize || 12) - 2
          }}>
            {row === 1 ? 'Active' : 'Pending'}
          </span>
        </div>
      </div>
    ))}
  </div>
);

// Data Grid
export const DataGrid: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
      backgroundColor: '#ffffff',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    {/* Toolbar */}
    <div style={{
      padding: '8px 12px',
      borderBottom: '1px solid #e5e7eb',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: '#f9fafb'
    }}>
      <div style={{ fontSize: '12px', color: '#6b7280' }}>
        {component.text || 'Data Grid'}
      </div>
      <div style={{ display: 'flex', gap: '4px' }}>
        <div style={{
          width: '20px',
          height: '20px',
          backgroundColor: '#e5e7eb',
          borderRadius: '3px',
          fontSize: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>⚙️</div>
        <div style={{
          width: '20px',
          height: '20px',
          backgroundColor: '#e5e7eb',
          borderRadius: '3px',
          fontSize: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>📊</div>
      </div>
    </div>
    {/* Grid */}
    <div style={{ flex: 1, overflow: 'hidden' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(4, 1fr)',
        gap: '1px',
        backgroundColor: '#e5e7eb',
        height: '100%'
      }}>
        {Array.from({ length: 16 }, (_, i) => (
          <div
            key={i}
            style={{
              backgroundColor: '#ffffff',
              padding: '4px',
              fontSize: '10px',
              color: '#6b7280',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {i < 4 ? `Col ${i + 1}` : `Data ${i - 3}`}
          </div>
        ))}
      </div>
    </div>
  </div>
);

// List
export const List: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
      backgroundColor: '#ffffff',
      overflow: 'hidden'
    }}
  >
    {['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5'].map((item, index) => (
      <div
        key={index}
        style={{
          padding: '12px 16px',
          borderBottom: index < 4 ? '1px solid #f3f4f6' : 'none',
          fontSize: '14px',
          color: '#374151',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}
      >
        <div style={{
          width: '8px',
          height: '8px',
          backgroundColor: '#3b82f6',
          borderRadius: '50%'
        }}></div>
        {component.text ? `${component.text} ${index + 1}` : item}
      </div>
    ))}
  </div>
);

// Chart (Bar)
export const BarChart: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
      backgroundColor: '#ffffff',
      padding: '16px',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    <div style={{
      fontSize: '14px',
      fontWeight: '600',
      color: '#111827',
      marginBottom: '16px'
    }}>
      {component.text || 'Bar Chart'}
    </div>
    <div style={{
      flex: 1,
      display: 'flex',
      alignItems: 'end',
      gap: '8px',
      paddingBottom: '20px'
    }}>
      {[60, 80, 45, 90, 70].map((height, index) => (
        <div
          key={index}
          style={{
            flex: 1,
            height: `${height}%`,
            backgroundColor: '#3b82f6',
            borderRadius: '2px 2px 0 0',
            position: 'relative'
          }}
        >
          <div style={{
            position: 'absolute',
            bottom: '-16px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '10px',
            color: '#6b7280'
          }}>
            Q{index + 1}
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Chart (Pie)
export const PieChart: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
      backgroundColor: '#ffffff',
      padding: '16px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}
  >
    <div style={{
      fontSize: '14px',
      fontWeight: '600',
      color: '#111827',
      marginBottom: '16px'
    }}>
      {component.text || 'Pie Chart'}
    </div>
    <div style={{
      width: '80px',
      height: '80px',
      borderRadius: '50%',
      background: `conic-gradient(
        #3b82f6 0deg 120deg,
        #10b981 120deg 200deg,
        #f59e0b 200deg 280deg,
        #ef4444 280deg 360deg
      )`,
      position: 'relative'
    }}>
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '40px',
        height: '40px',
        backgroundColor: '#ffffff',
        borderRadius: '50%'
      }}></div>
    </div>
    <div style={{
      marginTop: '12px',
      display: 'flex',
      gap: '8px',
      fontSize: '10px',
      color: '#6b7280'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <div style={{ width: '8px', height: '8px', backgroundColor: '#3b82f6', borderRadius: '50%' }}></div>
        A
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <div style={{ width: '8px', height: '8px', backgroundColor: '#10b981', borderRadius: '50%' }}></div>
        B
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <div style={{ width: '8px', height: '8px', backgroundColor: '#f59e0b', borderRadius: '50%' }}></div>
        C
      </div>
    </div>
  </div>
);

// Chart Column Component
export const ChartColumn: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      padding: '16px',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    {/* Chart Title */}
    <div
      style={{
        fontSize: component.fontSize || 16,
        fontWeight: '500',
        color: component.textColor || '#374151',
        marginBottom: '16px',
        textAlign: 'center'
      }}
    >
      {component.text || 'Column Chart'}
    </div>
    {/* Chart Area */}
    <div style={{ flex: 1, display: 'flex', alignItems: 'end', justifyContent: 'space-around', gap: '8px' }}>
      {[60, 80, 45, 90, 70].map((height, index) => (
        <div
          key={index}
          style={{
            width: '30px',
            height: `${height}%`,
            backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][index],
            borderRadius: '2px 2px 0 0',
            display: 'flex',
            alignItems: 'end',
            justifyContent: 'center',
            color: '#ffffff',
            fontSize: '10px',
            fontWeight: '500',
            paddingBottom: '4px'
          }}
        >
          {height}
        </div>
      ))}
    </div>
    {/* X-axis Labels */}
    <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '8px' }}>
      {['Q1', 'Q2', 'Q3', 'Q4', 'Q5'].map((label, index) => (
        <div key={index} style={{ fontSize: '12px', color: '#6b7280' }}>
          {label}
        </div>
      ))}
    </div>
  </div>
);

// Chart Line Component
export const ChartLine: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      padding: '16px',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    {/* Chart Title */}
    <div
      style={{
        fontSize: component.fontSize || 16,
        fontWeight: '500',
        color: component.textColor || '#374151',
        marginBottom: '16px',
        textAlign: 'center'
      }}
    >
      {component.text || 'Line Chart'}
    </div>
    {/* Chart Area */}
    <div
      style={{
        flex: 1,
        position: 'relative',
        background: 'linear-gradient(to right, transparent 0%, transparent 20%, transparent 40%, transparent 60%, transparent 80%, transparent 100%)',
        backgroundImage: 'repeating-linear-gradient(90deg, #f3f4f6 0, #f3f4f6 1px, transparent 1px, transparent 20%), repeating-linear-gradient(0deg, #f3f4f6 0, #f3f4f6 1px, transparent 1px, transparent 20%)'
      }}
    >
      {/* Line Path */}
      <svg
        style={{ width: '100%', height: '100%', position: 'absolute' }}
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
      >
        <polyline
          points="10,70 30,40 50,60 70,20 90,50"
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          vectorEffect="non-scaling-stroke"
        />
        {/* Data Points */}
        {[{x: 10, y: 70}, {x: 30, y: 40}, {x: 50, y: 60}, {x: 70, y: 20}, {x: 90, y: 50}].map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="2"
            fill="#3b82f6"
            vectorEffect="non-scaling-stroke"
          />
        ))}
      </svg>
    </div>
    {/* X-axis Labels */}
    <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '8px' }}>
      {['Jan', 'Feb', 'Mar', 'Apr', 'May'].map((label, index) => (
        <div key={index} style={{ fontSize: '12px', color: '#6b7280' }}>
          {label}
        </div>
      ))}
    </div>
  </div>
);

// Date Chooser Component
export const DateChooser: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      display: 'flex',
      alignItems: 'center',
      padding: '8px 12px',
      cursor: 'pointer'
    }}
  >
    <div style={{ fontSize: '16px', marginRight: '8px' }}>📅</div>
    <span
      style={{
        flex: 1,
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151'
      }}
    >
      {component.text || 'Select Date'}
    </span>
    <div style={{ fontSize: '12px', color: '#6b7280' }}>▼</div>
  </div>
);

// List with Icons Component
export const ListWithIcons: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 250,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      overflow: 'hidden'
    }}
  >
    {/* List Header */}
    <div
      style={{
        padding: '12px 16px',
        backgroundColor: '#f9fafb',
        borderBottom: '1px solid #e5e7eb',
        fontSize: component.fontSize || 14,
        fontWeight: '500',
        color: component.textColor || '#374151'
      }}
    >
      {component.text || 'List Items'}
    </div>
    {/* List Items */}
    <div>
      {[
        { icon: '📄', text: 'Document' },
        { icon: '📁', text: 'Folder' },
        { icon: '🖼️', text: 'Image' },
        { icon: '🎵', text: 'Audio' },
        { icon: '🎥', text: 'Video' }
      ].map((item, index) => (
        <div
          key={index}
          style={{
            padding: '12px 16px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            borderBottom: index < 4 ? '1px solid #f3f4f6' : 'none',
            cursor: 'pointer',
            fontSize: '14px',
            color: '#374151'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#f9fafb';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <span style={{ fontSize: '16px' }}>{item.icon}</span>
          <span>{item.text}</span>
        </div>
      ))}
    </div>
  </div>
);

export const dataComponents = {
  'Table': Table,
  'Data Grid': DataGrid,
  'List': List,
  'Bar Chart': BarChart,
  'Pie Chart': PieChart,
  'Chart Column': ChartColumn,
  'Chart Line': ChartLine,
  'Date Chooser': DateChooser,
  'List with Icons': ListWithIcons
};
