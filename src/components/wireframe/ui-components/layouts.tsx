import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Desktop Browser Window
export const DesktopBrowser: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '2px solid #d1d5db',
      borderRadius: '8px',
      backgroundColor: '#ffffff',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Browser header */}
    <div style={{
      height: '32px',
      backgroundColor: '#f3f4f6',
      borderBottom: '1px solid #d1d5db',
      display: 'flex',
      alignItems: 'center',
      padding: '0 12px',
      gap: '8px'
    }}>
      <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ef4444' }}></div>
      <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#f59e0b' }}></div>
      <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#10b981' }}></div>
      <div style={{
        marginLeft: '16px',
        flex: 1,
        height: '20px',
        backgroundColor: '#ffffff',
        borderRadius: '4px',
        border: '1px solid #d1d5db',
        display: 'flex',
        alignItems: 'center',
        padding: '0 8px',
        fontSize: '10px',
        color: '#6b7280'
      }}>
        {component.text || 'https://example.com'}
      </div>
    </div>
    {/* Content area */}
    <div style={{
      height: 'calc(100% - 32px)',
      backgroundColor: '#ffffff'
    }}>
      {/* Empty content area for user to add components */}
    </div>
  </div>
);

// Mobile Browser Window (Android)
export const AndroidBrowser: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '2px solid #d1d5db',
      borderRadius: '16px',
      backgroundColor: '#ffffff',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Status bar */}
    <div style={{
      height: '24px',
      backgroundColor: '#000000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 12px',
      fontSize: '10px',
      color: '#ffffff'
    }}>
      <span>9:41</span>
      <span>100%</span>
    </div>
    {/* Browser header */}
    <div style={{
      height: '48px',
      backgroundColor: '#f3f4f6',
      borderBottom: '1px solid #d1d5db',
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px'
    }}>
      <div style={{
        flex: 1,
        height: '32px',
        backgroundColor: '#ffffff',
        borderRadius: '16px',
        border: '1px solid #d1d5db',
        display: 'flex',
        alignItems: 'center',
        padding: '0 12px',
        fontSize: '10px',
        color: '#6b7280'
      }}>
        {component.text || 'Search or type URL'}
      </div>
    </div>
    {/* Content area */}
    <div style={{
      height: 'calc(100% - 72px)',
      backgroundColor: '#ffffff'
    }}>
      {/* Empty content area for user to add components */}
    </div>
  </div>
);

// iOS Browser Window
export const IOSBrowser: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '2px solid #d1d5db',
      borderRadius: '20px',
      backgroundColor: '#ffffff',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Status bar */}
    <div style={{
      height: '44px',
      backgroundColor: '#000000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative'
    }}>
      {/* Notch */}
      <div style={{
        width: '120px',
        height: '24px',
        backgroundColor: '#000000',
        borderRadius: '0 0 12px 12px',
        position: 'absolute',
        top: 0
      }}></div>
      <div style={{
        position: 'absolute',
        left: '16px',
        fontSize: '10px',
        color: '#ffffff'
      }}>9:41</div>
      <div style={{
        position: 'absolute',
        right: '16px',
        fontSize: '10px',
        color: '#ffffff'
      }}>100%</div>
    </div>
    {/* Safari header */}
    <div style={{
      height: '48px',
      backgroundColor: '#f3f4f6',
      borderBottom: '1px solid #d1d5db',
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px'
    }}>
      <div style={{
        flex: 1,
        height: '32px',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        border: '1px solid #d1d5db',
        display: 'flex',
        alignItems: 'center',
        padding: '0 12px',
        fontSize: '10px',
        color: '#6b7280'
      }}>
        {component.text || 'Search or enter website name'}
      </div>
    </div>
    {/* Content area */}
    <div style={{
      height: 'calc(100% - 92px)',
      backgroundColor: '#ffffff'
    }}>
      {/* Empty content area for user to add components */}
    </div>
  </div>
);

// Inspector Panel
export const InspectorPanel: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      backgroundColor: '#f9fafb',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Header */}
    <div style={{
      height: '40px',
      backgroundColor: '#f3f4f6',
      borderBottom: '1px solid #d1d5db',
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px',
      fontSize: '12px',
      fontWeight: '600',
      color: '#374151'
    }}>
      {component.text || 'Inspector'}
    </div>
    {/* Content */}
    <div style={{
      height: 'calc(100% - 40px)',
      padding: '16px',
      fontSize: '11px',
      color: '#6b7280'
    }}>
      <div style={{ marginBottom: '12px' }}>
        <div style={{ fontWeight: '500', marginBottom: '4px' }}>Properties</div>
        <div style={{ padding: '8px', backgroundColor: '#ffffff', border: '1px solid #e5e7eb', borderRadius: '4px' }}>
          Width: 200px<br />
          Height: 100px<br />
          Position: Absolute
        </div>
      </div>
      <div>
        <div style={{ fontWeight: '500', marginBottom: '4px' }}>Styles</div>
        <div style={{ padding: '8px', backgroundColor: '#ffffff', border: '1px solid #e5e7eb', borderRadius: '4px' }}>
          Background: #ffffff<br />
          Border: 1px solid<br />
          Border-radius: 4px
        </div>
      </div>
    </div>
  </div>
);

// Modal
export const Modal: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '2px solid #d1d5db',
      borderRadius: '8px',
      backgroundColor: '#ffffff',
      position: 'relative',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      overflow: 'hidden'
    }}
  >
    {/* Header */}
    <div style={{
      height: '48px',
      backgroundColor: '#f9fafb',
      borderBottom: '1px solid #e5e7eb',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 16px'
    }}>
      <div style={{ fontSize: '14px', fontWeight: '600', color: '#111827' }}>
        {component.text || 'Modal Title'}
      </div>
      <div style={{
        width: '24px',
        height: '24px',
        borderRadius: '4px',
        backgroundColor: '#f3f4f6',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '16px',
        color: '#6b7280',
        cursor: 'pointer'
      }}>
        ×
      </div>
    </div>
    {/* Content */}
    <div style={{
      height: 'calc(100% - 48px)',
      padding: '16px',
      backgroundColor: '#ffffff'
    }}>
      {/* Empty content area for user to add components */}
    </div>
  </div>
);

// Generic Browser Component
export const Browser: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 800,
      height: component.height || 600,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '8px',
      overflow: 'hidden',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    }}
  >
    {/* Browser Header */}
    <div
      style={{
        height: '40px',
        backgroundColor: '#f3f4f6',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        padding: '0 12px',
        gap: '8px'
      }}
    >
      <div style={{ display: 'flex', gap: '6px' }}>
        <div style={{ width: '12px', height: '12px', backgroundColor: '#ef4444', borderRadius: '50%' }}></div>
        <div style={{ width: '12px', height: '12px', backgroundColor: '#f59e0b', borderRadius: '50%' }}></div>
        <div style={{ width: '12px', height: '12px', backgroundColor: '#10b981', borderRadius: '50%' }}></div>
      </div>
      <div
        style={{
          flex: 1,
          height: '24px',
          backgroundColor: '#ffffff',
          border: '1px solid #d1d5db',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 8px',
          fontSize: '12px',
          color: '#6b7280'
        }}
      >
        {component.text || 'https://example.com'}
      </div>
    </div>
    {/* Browser Content */}
    <div
      style={{
        flex: 1,
        padding: '20px',
        fontSize: '14px',
        color: '#6b7280',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: 'calc(100% - 40px)'
      }}
    >
      Browser Content Area
    </div>
  </div>
);

// iPad Component
export const IPad: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 300,
      backgroundColor: component.backgroundColor || '#1f2937',
      borderRadius: '20px',
      padding: '20px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative'
    }}
  >
    {/* iPad Screen */}
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '14px',
        color: '#6b7280'
      }}
    >
      {component.text || 'iPad Screen'}
    </div>
    {/* Home Button */}
    <div
      style={{
        position: 'absolute',
        bottom: '8px',
        width: '16px',
        height: '16px',
        backgroundColor: '#374151',
        borderRadius: '50%'
      }}
    />
  </div>
);

// iPhone Component
export const IPhone: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 400,
      backgroundColor: component.backgroundColor || '#1f2937',
      borderRadius: '25px',
      padding: '15px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      position: 'relative'
    }}
  >
    {/* iPhone Screen */}
    <div
      style={{
        width: '100%',
        flex: 1,
        backgroundColor: '#ffffff',
        borderRadius: '15px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: '#6b7280'
      }}
    >
      {component.text || 'iPhone Screen'}
    </div>
    {/* Home Indicator */}
    <div
      style={{
        width: '40px',
        height: '4px',
        backgroundColor: '#374151',
        borderRadius: '2px',
        marginTop: '8px'
      }}
    />
  </div>
);

// H Splitter Component
export const HSplitter: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    {/* Top Panel */}
    <div
      style={{
        flex: 1,
        padding: '16px',
        backgroundColor: '#f9fafb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '14px',
        color: '#6b7280'
      }}
    >
      Top Panel
    </div>
    {/* Splitter */}
    <div
      style={{
        height: '8px',
        backgroundColor: '#e5e7eb',
        cursor: 'row-resize',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <div style={{ width: '20px', height: '2px', backgroundColor: '#9ca3af' }} />
    </div>
    {/* Bottom Panel */}
    <div
      style={{
        flex: 1,
        padding: '16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '14px',
        color: '#6b7280'
      }}
    >
      {component.text || 'Bottom Panel'}
    </div>
  </div>
);

// H Curly Bracket Component
export const HCurlyBracket: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 100,
      height: component.height || 60,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 24,
      color: component.textColor || '#6b7280',
      fontFamily: 'monospace'
    }}
  >
    {component.text || '{ }'}
  </div>
);

// H Rule Component
export const HRule: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 20,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}
  >
    <hr
      style={{
        width: '100%',
        border: 'none',
        borderTop: `2px solid ${component.borderColor || '#d1d5db'}`,
        margin: 0
      }}
    />
  </div>
);

// V Splitter Component
export const VSplitter: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 20,
      height: component.height || 300,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}
  >
    <div
      style={{
        width: '100%',
        height: '2px',
        backgroundColor: component.borderColor || '#d1d5db',
        margin: '4px 0'
      }}
    />
    <div
      style={{
        width: '8px',
        height: '20px',
        backgroundColor: component.backgroundColor || '#f3f4f6',
        border: `1px solid ${component.borderColor || '#d1d5db'}`,
        borderRadius: '2px',
        cursor: 'ns-resize'
      }}
    />
    <div
      style={{
        width: '100%',
        height: '2px',
        backgroundColor: component.borderColor || '#d1d5db',
        margin: '4px 0'
      }}
    />
  </div>
);

// V Tabs Component
export const VTabs: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 300,
      display: 'flex',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      backgroundColor: component.backgroundColor || '#ffffff'
    }}
  >
    {/* Tab buttons on the left */}
    <div
      style={{
        width: '80px',
        backgroundColor: '#f9fafb',
        borderRight: `1px solid ${component.borderColor || '#d1d5db'}`,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {['Tab 1', 'Tab 2', 'Tab 3'].map((tab, index) => (
        <div
          key={index}
          style={{
            padding: '12px 8px',
            fontSize: component.fontSize || 12,
            color: component.textColor || '#374151',
            backgroundColor: index === 0 ? '#ffffff' : 'transparent',
            borderBottom: `1px solid ${component.borderColor || '#d1d5db'}`,
            cursor: 'pointer',
            textAlign: 'center',
            fontWeight: index === 0 ? '500' : '400'
          }}
        >
          {index === 0 && component.text ? component.text : tab}
        </div>
      ))}
    </div>
    {/* Tab content */}
    <div
      style={{
        flex: 1,
        padding: '16px',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#6b7280'
      }}
    >
      Tab content area
    </div>
  </div>
);

// V Slider Component
export const VSlider: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 30,
      height: component.height || 200,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '10px'
    }}
  >
    <div
      style={{
        width: '4px',
        height: '100%',
        backgroundColor: component.backgroundColor || '#e5e7eb',
        borderRadius: '2px',
        position: 'relative'
      }}
    >
      <div
        style={{
          width: '16px',
          height: '16px',
          backgroundColor: component.borderColor || '#3b82f6',
          borderRadius: '50%',
          position: 'absolute',
          top: '30%',
          left: '-6px',
          cursor: 'pointer',
          border: '2px solid #ffffff',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.3)'
        }}
      />
    </div>
  </div>
);

// V Scroll Bar Component
export const VScrollBar: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 16,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#f3f4f6',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      position: 'relative'
    }}
  >
    {/* Scroll track */}
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'relative'
      }}
    >
      {/* Scroll thumb */}
      <div
        style={{
          width: '12px',
          height: '40px',
          backgroundColor: '#9ca3af',
          borderRadius: '6px',
          position: 'absolute',
          top: '20%',
          left: '2px',
          cursor: 'pointer'
        }}
      />
    </div>
  </div>
);

// V Rule Component
export const VRule: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 20,
      height: component.height || 300,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}
  >
    <div
      style={{
        width: '2px',
        height: '100%',
        backgroundColor: component.borderColor || '#d1d5db'
      }}
    />
  </div>
);

// V Curly Bracket Component
export const VCurlyBracket: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 30,
      height: component.height || 200,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 48,
      color: component.textColor || '#6b7280',
      fontFamily: 'monospace'
    }}
  >
    {component.text || '{'}
  </div>
);

// Window Component
export const Window: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 300,
      border: `2px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '8px',
      backgroundColor: component.backgroundColor || '#ffffff',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Window title bar */}
    <div
      style={{
        height: '32px',
        backgroundColor: '#f3f4f6',
        borderBottom: `1px solid ${component.borderColor || '#d1d5db'}`,
        display: 'flex',
        alignItems: 'center',
        padding: '0 12px',
        gap: '8px'
      }}
    >
      {/* Window controls */}
      <div style={{ display: 'flex', gap: '4px' }}>
        <div style={{ width: '12px', height: '12px', backgroundColor: '#ef4444', borderRadius: '50%' }} />
        <div style={{ width: '12px', height: '12px', backgroundColor: '#f59e0b', borderRadius: '50%' }} />
        <div style={{ width: '12px', height: '12px', backgroundColor: '#10b981', borderRadius: '50%' }} />
      </div>
      <span style={{
        fontSize: component.fontSize || 12,
        color: component.textColor || '#374151',
        fontWeight: '500'
      }}>
        {component.text || 'Window Title'}
      </span>
    </div>
    {/* Window content */}
    <div style={{
      height: 'calc(100% - 32px)',
      padding: '16px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    }}>
      Window content area
    </div>
  </div>
);

export const layoutComponents = {
  'Desktop Browser': DesktopBrowser,
  'Android Browser': AndroidBrowser,
  'iOS Browser': IOSBrowser,
  'Inspector Panel': InspectorPanel,
  'Modal': Modal,
  'Browser': Browser,
  'iPad': IPad,
  'iPhone': IPhone,
  'H Splitter': HSplitter,
  'H Curly Bracket': HCurlyBracket,
  'H Rule': HRule,
  'V Splitter': VSplitter,
  'V Tabs': VTabs,
  'V Slider': VSlider,
  'V Scroll Bar': VScrollBar,
  'V Rule': VRule,
  'V Curly Bracket': VCurlyBracket,
  'Window': Window
};
