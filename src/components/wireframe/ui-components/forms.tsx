import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  const borderRadius = component.borderRadius !== undefined
    ? (component.borderRadius === 9999 ? '50px' : `${component.borderRadius}px`)
    : defaultStyles.borderRadius || '4px';

  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || 'transparent',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#d1d5db'}`
        : defaultStyles.border || '1px solid #d1d5db',
    borderRadius,
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Button
export const Button: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const iconPosition = component.iconPosition || 'left';
  const hasIcon = component.icon && component.icon.trim() !== '';
  const hasText = component.text && component.text.trim() !== '';
  const componentState = component.componentState || 'normal';

  // Get state-specific styles
  const getStateStyles = () => {
    const borderRadius = component.borderRadius !== undefined
      ? (component.borderRadius === 9999 ? '50px' : `${component.borderRadius}px`)
      : '6px';

    const baseStyles = {
      borderRadius,
      backgroundColor: component.backgroundColor || '#3b82f6',
      color: component.textColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontWeight: '500',
      cursor: 'pointer'
    };

    switch (componentState) {
      case 'hover':
        return {
          ...baseStyles,
          backgroundColor: component.backgroundColor ?
            `color-mix(in srgb, ${component.backgroundColor} 80%, white)` :
            '#2563eb',
          transform: 'translateY(-1px)',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        };
      case 'active':
        return {
          ...baseStyles,
          backgroundColor: component.backgroundColor ?
            `color-mix(in srgb, ${component.backgroundColor} 70%, black)` :
            '#1d4ed8',
          transform: 'translateY(1px)',
          boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)'
        };
      case 'disabled':
        return {
          ...baseStyles,
          backgroundColor: '#9ca3af',
          color: '#6b7280',
          cursor: 'not-allowed',
          opacity: 0.6
        };
      case 'selected':
        return {
          ...baseStyles,
          backgroundColor: component.backgroundColor || '#3b82f6',
          boxShadow: '0 0 0 2px #fbbf24',
          outline: '2px solid #f59e0b'
        };
      default:
        return baseStyles;
    }
  };

  const renderContent = () => {
    if (!hasIcon && !hasText) {
      return 'Button';
    }

    if (!hasIcon) {
      return component.text;
    }

    if (!hasText) {
      return <span style={{ fontSize: '16px' }}>{component.icon}</span>;
    }

    // Both icon and text
    const iconElement = <span style={{ fontSize: '16px' }}>{component.icon}</span>;
    const textElement = <span>{component.text}</span>;

    const gap = '8px';

    switch (iconPosition) {
      case 'right':
        return (
          <>
            {textElement}
            <span style={{ marginLeft: gap }}>{iconElement}</span>
          </>
        );
      case 'top':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '4px' }}>
            {iconElement}
            {textElement}
          </div>
        );
      case 'bottom':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '4px' }}>
            {textElement}
            {iconElement}
          </div>
        );
      case 'left':
      default:
        return (
          <>
            <span style={{ marginRight: gap }}>{iconElement}</span>
            {textElement}
          </>
        );
    }
  };

  return (
    <div
      style={getComponentStyle(component, isSelected, getStateStyles())}
    >
      {renderContent()}
    </div>
  );
};

// Radio Button
export const RadioButton: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      border: 'none'
    })}
  >
    <div style={{
      width: '16px',
      height: '16px',
      borderRadius: '50%',
      border: `2px solid ${component.borderColor || '#d1d5db'}`,
      backgroundColor: component.backgroundColor || '#ffffff',
      position: 'relative'
    }}>
      <div style={{
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: component.textColor || '#3b82f6',
        position: 'absolute',
        top: '2px',
        left: '2px'
      }}></div>
    </div>
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}>
      {component.text || 'Radio Option'}
    </span>
  </div>
);

// Checkbox
export const Checkbox: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      border: 'none'
    })}
  >
    <div style={{
      width: '16px',
      height: '16px',
      borderRadius: '3px',
      border: `2px solid ${component.borderColor || '#d1d5db'}`,
      backgroundColor: component.backgroundColor || '#3b82f6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#ffffff',
      fontSize: '10px'
    }}>
      ✓
    </div>
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}>
      {component.text || 'Checkbox Option'}
    </span>
  </div>
);

// Dropdown/Combobox
export const Dropdown: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      borderRadius: '6px',
      backgroundColor: component.backgroundColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 12px'
    })}
  >
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}>
      {component.text || 'Select option...'}
    </span>
    <span style={{ color: component.textColor || '#6b7280' }}>▼</span>
  </div>
);

// Text Input
export const TextInput: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      borderRadius: '6px',
      backgroundColor: component.backgroundColor || '#ffffff',
      color: component.textColor || '#6b7280',
      display: 'flex',
      alignItems: 'center',
      padding: '0 12px'
    })}
  >
    {component.text || 'Enter text...'}
  </div>
);

// Text Area
export const TextArea: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      borderRadius: '6px',
      backgroundColor: component.backgroundColor || '#ffffff',
      padding: '12px',
      resize: 'none'
    })}
  >
    {component.text || 'Enter your message...'}
  </div>
);

// Slider
export const Slider: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      display: 'flex',
      alignItems: 'center',
      padding: '0 8px',
      border: 'none'
    })}
  >
    <div style={{
      flex: 1,
      height: '4px',
      backgroundColor: component.borderColor || '#e5e7eb',
      borderRadius: '2px',
      position: 'relative'
    }}>
      <div style={{
        width: '40%',
        height: '100%',
        backgroundColor: component.textColor || '#3b82f6',
        borderRadius: '2px'
      }}></div>
      <div style={{
        width: '16px',
        height: '16px',
        backgroundColor: component.textColor || '#3b82f6',
        borderRadius: '50%',
        position: 'absolute',
        top: '-6px',
        left: '40%',
        transform: 'translateX(-50%)',
        border: '2px solid #ffffff',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}></div>
    </div>
  </div>
);

// Progress Bar
export const ProgressBar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      gap: '4px',
      border: 'none'
    })}
  >
    <div style={{
      fontSize: component.fontSize || 12,
      color: component.textColor || '#6b7280',
      display: 'flex',
      justifyContent: 'space-between'
    }}>
      <span>{component.text || 'Progress'}</span>
      <span>65%</span>
    </div>
    <div style={{
      height: '8px',
      backgroundColor: component.borderColor || '#e5e7eb',
      borderRadius: '4px',
      overflow: 'hidden'
    }}>
      <div style={{
        width: '65%',
        height: '100%',
        backgroundColor: component.backgroundColor || '#10b981',
        borderRadius: '4px'
      }}></div>
    </div>
  </div>
);

// Search Box
export const SearchBox: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      borderRadius: '20px',
      backgroundColor: component.backgroundColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      padding: '0 16px',
      gap: '8px'
    })}
  >
    <span style={{ fontSize: '16px' }}>🔍</span>
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    }}>
      {component.text || 'Search...'}
    </span>
  </div>
);

// Date Picker
export const DatePicker: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      borderRadius: '6px',
      backgroundColor: component.backgroundColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 12px'
    })}
  >
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}>
      {component.text || 'Select date...'}
    </span>
    <span style={{ color: component.textColor || '#6b7280' }}>📅</span>
  </div>
);

// Accordion Component
export const Accordion: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      overflow: 'hidden'
    }}
  >
    {/* Accordion Header */}
    <div
      style={{
        padding: '12px 16px',
        backgroundColor: '#f9fafb',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: component.fontSize || 14,
        fontWeight: '500',
        color: component.textColor || '#374151'
      }}
    >
      <span>{component.text || 'Accordion Section'}</span>
      <span style={{ fontSize: '12px' }}>▼</span>
    </div>
    {/* Accordion Content */}
    <div
      style={{
        padding: '16px',
        fontSize: '14px',
        color: '#6b7280',
        lineHeight: '1.5'
      }}
    >
      Accordion content goes here. This section can be expanded or collapsed.
    </div>
  </div>
);

// Alert Box Component
export const AlertBox: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 60,
      backgroundColor: component.backgroundColor || '#fef3c7',
      border: `1px solid ${component.borderColor || '#f59e0b'}`,
      borderRadius: '6px',
      padding: '12px 16px',
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    }}
  >
    <div style={{ fontSize: '18px', color: '#f59e0b' }}>⚠️</div>
    <div
      style={{
        fontSize: component.fontSize || 14,
        color: component.textColor || '#92400e',
        flex: 1
      }}
    >
      {component.text || 'This is an alert message'}
    </div>
  </div>
);

// Button Bar Component
export const ButtonBar: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 40,
      display: 'flex',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      overflow: 'hidden',
      backgroundColor: component.backgroundColor || '#ffffff'
    }}
  >
    {['Button 1', 'Button 2', 'Button 3'].map((buttonText, index) => (
      <div
        key={index}
        style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: component.fontSize || 14,
          color: component.textColor || '#374151',
          borderRight: index < 2 ? '1px solid #d1d5db' : 'none',
          cursor: 'pointer',
          backgroundColor: index === 0 ? '#f3f4f6' : 'transparent'
        }}
      >
        {buttonText}
      </div>
    ))}
  </div>
);

// Color Picker Component
export const ColorPicker: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 120,
      height: component.height || 40,
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    }}
  >
    <div
      style={{
        width: '32px',
        height: '32px',
        backgroundColor: component.backgroundColor || '#3b82f6',
        border: `1px solid ${component.borderColor || '#d1d5db'}`,
        borderRadius: '4px',
        cursor: 'pointer'
      }}
    />
    <span
      style={{
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151'
      }}
    >
      {component.text || 'Color'}
    </span>
  </div>
);

// ComboBox Component
export const ComboBox: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      display: 'flex',
      alignItems: 'center'
    }}
  >
    <input
      type="text"
      placeholder={component.text || 'Type or select...'}
      style={{
        flex: 1,
        border: 'none',
        outline: 'none',
        padding: '8px 12px',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151',
        backgroundColor: 'transparent'
      }}
    />
    <div
      style={{
        padding: '8px 12px',
        borderLeft: '1px solid #d1d5db',
        cursor: 'pointer',
        fontSize: '12px',
        color: '#6b7280'
      }}
    >
      ▼
    </div>
  </div>
);

// Field Set Component
export const FieldSet: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <fieldset
    style={{
      width: component.width || 300,
      height: component.height || 150,
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      padding: '16px',
      margin: '0',
      backgroundColor: component.backgroundColor || '#ffffff'
    }}
  >
    <legend
      style={{
        fontSize: component.fontSize || 14,
        fontWeight: '500',
        color: component.textColor || '#374151',
        padding: '0 8px'
      }}
    >
      {component.text || 'Field Set'}
    </legend>
    <div
      style={{
        fontSize: '14px',
        color: '#6b7280',
        lineHeight: '1.5'
      }}
    >
      Form fields and controls go here
    </div>
  </fieldset>
);

// Checkbox Group Component
export const CheckboxGroup: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 120,
      padding: '8px',
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px'
    }}
  >
    <div
      style={{
        fontSize: component.fontSize || 14,
        fontWeight: '500',
        color: component.textColor || '#374151',
        marginBottom: '8px'
      }}
    >
      {component.text || 'Options'}
    </div>
    {['Option 1', 'Option 2', 'Option 3'].map((option, index) => (
      <div
        key={index}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '6px',
          fontSize: '14px',
          color: '#374151'
        }}
      >
        <div
          style={{
            width: '16px',
            height: '16px',
            border: '1px solid #d1d5db',
            borderRadius: '3px',
            backgroundColor: index === 0 ? '#3b82f6' : '#ffffff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            color: '#ffffff'
          }}
        >
          {index === 0 ? '✓' : ''}
        </div>
        <span>{option}</span>
      </div>
    ))}
  </div>
);

// Menu Bar Component
export const MenuBar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 30,
      backgroundColor: component.backgroundColor || '#f3f4f6',
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      display: 'flex',
      alignItems: 'center',
      fontSize: component.fontSize || 12,
      color: component.textColor || '#374151'
    }}
  >
    {['File', 'Edit', 'View', 'Help'].map((item, index) => (
      <div
        key={index}
        style={{
          padding: '4px 12px',
          cursor: 'pointer',
          borderRight: index < 3 ? '1px solid #d1d5db' : 'none'
        }}
      >
        {index === 0 && component.text ? component.text : item}
      </div>
    ))}
  </div>
);

// Multiline Button Component
export const MultilineButton: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const borderRadius = component.borderRadius !== undefined
    ? (component.borderRadius === 9999 ? '50px' : `${component.borderRadius}px`)
    : '6px';

  return (
    <div
      style={{
        width: component.width || 120,
        height: component.height || 60,
        backgroundColor: component.backgroundColor || '#3b82f6',
        color: component.textColor || '#ffffff',
        border: isSelected ? '2px solid #3b82f6' : `1px solid ${component.borderColor || '#2563eb'}`,
        borderRadius,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: component.fontSize || 12,
        fontWeight: '500',
        cursor: 'pointer',
        userSelect: 'none',
        textAlign: 'center',
        padding: '8px'
      }}
    >
      <div>{component.text || 'Multi-line'}</div>
      <div style={{ fontSize: '10px', opacity: 0.8 }}>Button</div>
    </div>
  );
};

// Number Stepper Component
export const NumberStepper: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 100,
      height: component.height || 32,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: component.backgroundColor || '#ffffff'
    }}
  >
    <input
      type="number"
      value={component.text || '0'}
      readOnly
      style={{
        flex: 1,
        border: 'none',
        outline: 'none',
        padding: '0 8px',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151',
        backgroundColor: 'transparent'
      }}
    />
    <div style={{ display: 'flex', flexDirection: 'column', borderLeft: '1px solid #d1d5db' }}>
      <button style={{
        width: '20px',
        height: '16px',
        border: 'none',
        backgroundColor: '#f3f4f6',
        cursor: 'pointer',
        fontSize: '10px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>▲</button>
      <button style={{
        width: '20px',
        height: '16px',
        border: 'none',
        backgroundColor: '#f3f4f6',
        cursor: 'pointer',
        fontSize: '10px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>▼</button>
    </div>
  </div>
);

// ON/OFF Switch Component
export const OnOffSwitch: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const isOn = component.data?.isOn !== false; // Default to true

  return (
    <div
      style={{
        width: component.width || 60,
        height: component.height || 30,
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}
    >
      <div
        style={{
          width: '50px',
          height: '24px',
          backgroundColor: isOn ? '#10b981' : '#d1d5db',
          borderRadius: '12px',
          border: isSelected ? '2px solid #3b82f6' : 'none',
          position: 'relative',
          cursor: 'pointer',
          transition: 'background-color 0.2s'
        }}
      >
        <div
          style={{
            width: '20px',
            height: '20px',
            backgroundColor: '#ffffff',
            borderRadius: '50%',
            position: 'absolute',
            top: '2px',
            left: isOn ? '28px' : '2px',
            transition: 'left 0.2s',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.3)'
          }}
        />
      </div>
      {component.text && (
        <span style={{
          fontSize: component.fontSize || 14,
          color: component.textColor || '#374151'
        }}>
          {component.text}
        </span>
      )}
    </div>
  );
};

// Playback Controls Component
export const PlaybackControls: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 40,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '6px',
      backgroundColor: component.backgroundColor || '#f9fafb',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      padding: '8px'
    }}
  >
    {['⏮', '⏸', '▶', '⏭'].map((icon, index) => (
      <button
        key={index}
        style={{
          width: '32px',
          height: '32px',
          border: '1px solid #d1d5db',
          borderRadius: '4px',
          backgroundColor: '#ffffff',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px',
          color: component.textColor || '#374151'
        }}
      >
        {icon}
      </button>
    ))}
  </div>
);

// Pointy Button Component
export const PointyButton: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 120,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#3b82f6',
      color: component.textColor || '#ffffff',
      border: isSelected ? '2px solid #3b82f6' : `1px solid ${component.borderColor || '#2563eb'}`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 14,
      fontWeight: '500',
      cursor: 'pointer',
      userSelect: 'none',
      position: 'relative',
      clipPath: 'polygon(0 0, calc(100% - 10px) 0, 100% 50%, calc(100% - 10px) 100%, 0 100%)'
    }}
  >
    {component.text || 'Pointy Button'}
  </div>
);

// Time Picker Component
export const TimePicker: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 120,
      height: component.height || 32,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      padding: '0 8px',
      gap: '4px'
    }}
  >
    <span style={{
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}>
      {component.text || '12:00 PM'}
    </span>
    <div style={{
      marginLeft: 'auto',
      fontSize: '12px',
      color: '#6b7280'
    }}>
      🕐
    </div>
  </div>
);

// Rectangle Component (Basic Shape)
export const Rectangle: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 100,
      backgroundColor: component.backgroundColor || '#f3f4f6',
      border: isSelected ? '2px solid #3b82f6' : `2px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: component.data?.borderRadius || '0px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    }}
  >
    {component.text || ''}
  </div>
);

export const formComponents = {
  'Button': Button,
  'Radio Button': RadioButton,
  'Checkbox': Checkbox,
  'Dropdown': Dropdown,
  'Text Input': TextInput,
  'Text Area': TextArea,
  'Slider': Slider,
  'Progress Bar': ProgressBar,
  'Search Box': SearchBox,
  'Date Picker': DatePicker,
  'Accordion': Accordion,
  'Alert Box': AlertBox,
  'Button Bar': ButtonBar,
  'Color Picker': ColorPicker,
  'ComboBox': ComboBox,
  'Field Set': FieldSet,
  'Checkbox Group': CheckboxGroup,
  'Menu Bar': MenuBar,
  'Multiline Button': MultilineButton,
  'Number Stepper': NumberStepper,
  'ON/OFF Switch': OnOffSwitch,
  'Playback Controls': PlaybackControls,
  'Pointy Button': PointyButton,
  'Time Picker': TimePicker,
  'Rectangle': Rectangle
};
