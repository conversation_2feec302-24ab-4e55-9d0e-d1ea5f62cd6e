import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || 'transparent',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#d1d5db'}`
        : defaultStyles.border || '1px solid #d1d5db',
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Image Placeholder with Upload Support
export const ImagePlaceholder: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const [isUploading, setIsUploading] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        // Update component with image URL
        if (component.onUpdate) {
          component.onUpdate({ ...component, data: { ...component.data, imageUrl } });
        }
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUrlInput = () => {
    const url = prompt('Enter image URL:');
    if (url) {
      // Update component with image URL
      if (component.onUpdate) {
        component.onUpdate({ ...component, data: { ...component.data, imageUrl: url } });
      }
    }
  };

  const hasImage = component.data?.imageUrl;

  if (hasImage) {
    return (
      <div
        style={getComponentStyle(component, isSelected, {
          border: isSelected ? '2px solid #3b82f6' : 'none',
          backgroundColor: 'transparent',
          overflow: 'hidden',
          position: 'relative'
        })}
        onDoubleClick={() => fileInputRef.current?.click()}
      >
        <img
          src={component.data.imageUrl}
          alt={component.text || 'Uploaded image'}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }}
        />
        {isSelected && (
          <div
            style={{
              position: 'absolute',
              top: '4px',
              right: '4px',
              display: 'flex',
              gap: '4px'
            }}
          >
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{
                padding: '4px 8px',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '10px',
                cursor: 'pointer'
              }}
            >
              📁
            </button>
            <button
              onClick={handleUrlInput}
              style={{
                padding: '4px 8px',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '10px',
                cursor: 'pointer'
              }}
            >
              🔗
            </button>
          </div>
        )}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </div>
    );
  }

  return (
    <div
      style={getComponentStyle(component, isSelected, {
        border: isSelected ? '2px solid #3b82f6' : `2px dashed ${component.borderColor || '#d1d5db'}`,
        backgroundColor: component.backgroundColor || '#f9fafb',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '8px',
        color: component.textColor || '#6b7280',
        cursor: 'pointer'
      })}
      onClick={() => fileInputRef.current?.click()}
      onDoubleClick={handleUrlInput}
    >
      {isUploading ? (
        <div style={{ fontSize: '24px' }}>⏳</div>
      ) : (
        <div style={{ fontSize: '24px' }}>🖼️</div>
      )}
      <div style={{
        fontSize: component.fontSize || 12,
        textAlign: 'center',
        color: component.textColor || '#6b7280',
        fontWeight: '500'
      }}>
        {isUploading ? 'Uploading...' : (component.text || 'Click to Upload')}
      </div>
      <div style={{
        fontSize: '10px',
        color: component.textColor || '#9ca3af',
        textAlign: 'center',
        marginTop: '4px'
      }}>
        {isUploading ? '' : 'or Double-click for URL'}
      </div>
      <div style={{
        fontSize: '10px',
        color: component.textColor || '#9ca3af'
      }}>
        {component.width} × {component.height}
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        style={{ display: 'none' }}
      />
    </div>
  );
};

// Video Placeholder
export const VideoPlaceholder: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      border: isSelected ? '2px solid #3b82f6' : `2px dashed ${component.borderColor || '#d1d5db'}`,
      backgroundColor: component.backgroundColor || '#111827',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      color: component.textColor || '#ffffff',
      position: 'relative'
    })}
  >
    <div style={{
      width: '48px',
      height: '48px',
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '20px'
    }}>
      ▶️
    </div>
    <div style={{
      fontSize: component.fontSize || 12,
      textAlign: 'center',
      color: component.textColor || '#ffffff'
    }}>
      {component.text || 'Video Placeholder'}
    </div>
    <div style={{
      position: 'absolute',
      bottom: '8px',
      right: '8px',
      fontSize: '10px',
      color: component.textColor || '#9ca3af'
    }}>
      {component.width} × {component.height}
    </div>
  </div>
);

// Icon Placeholder
export const IconPlaceholder: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      backgroundColor: component.backgroundColor || '#f3f4f6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '6px',
      color: component.textColor || '#6b7280'
    })}
  >
    <div style={{
      fontSize: component.fontSize || Math.min(component.width * 0.6, component.height * 0.6, 24) + 'px',
      color: component.textColor || '#6b7280'
    }}>
      {component.text || '⭐'}
    </div>
  </div>
);

// Avatar/Profile Icon
export const Avatar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      backgroundColor: component.backgroundColor || '#f3f4f6',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: component.textColor || '#6b7280',
      fontSize: component.fontSize || Math.min(component.width * 0.4, component.height * 0.4, 20) + 'px',
      overflow: 'hidden'
    })}
  >
    {component.text || '👤'}
  </div>
);

// Map Placeholder
export const MapPlaceholder: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      backgroundColor: '#f0f9ff',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      color: '#0369a1',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Map grid pattern */}
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundImage: `
        linear-gradient(rgba(3, 105, 161, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(3, 105, 161, 0.1) 1px, transparent 1px)
      `,
      backgroundSize: '20px 20px'
    }}></div>
    <div style={{ fontSize: '24px', zIndex: 1 }}>🗺️</div>
    <div style={{ fontSize: '12px', textAlign: 'center', zIndex: 1 }}>
      {component.text || 'Map View'}
    </div>
    {/* Location pin */}
    <div style={{
      position: 'absolute',
      top: '40%',
      left: '60%',
      fontSize: '16px',
      zIndex: 2
    }}>
      📍
    </div>
  </div>
);

// Calendar Component
export const Calendar: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 280,
      height: component.height || 300,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '8px',
      overflow: 'hidden'
    }}
  >
    {/* Calendar Header */}
    <div
      style={{
        padding: '12px 16px',
        backgroundColor: '#f9fafb',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}
    >
      <button style={{ background: 'none', border: 'none', fontSize: '16px', cursor: 'pointer' }}>‹</button>
      <span style={{ fontSize: component.fontSize || 16, fontWeight: '500', color: component.textColor || '#374151' }}>
        {component.text || 'December 2024'}
      </span>
      <button style={{ background: 'none', border: 'none', fontSize: '16px', cursor: 'pointer' }}>›</button>
    </div>
    {/* Days of Week */}
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', backgroundColor: '#f9fafb' }}>
      {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
        <div
          key={index}
          style={{
            padding: '8px',
            textAlign: 'center',
            fontSize: '12px',
            fontWeight: '500',
            color: '#6b7280'
          }}
        >
          {day}
        </div>
      ))}
    </div>
    {/* Calendar Grid */}
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', flex: 1 }}>
      {Array.from({ length: 35 }, (_, i) => {
        const day = i - 6 + 1;
        const isCurrentMonth = day > 0 && day <= 31;
        const isToday = day === 15;
        return (
          <div
            key={i}
            style={{
              padding: '8px',
              textAlign: 'center',
              fontSize: '14px',
              color: isCurrentMonth ? '#374151' : '#d1d5db',
              backgroundColor: isToday ? '#3b82f6' : 'transparent',
              cursor: isCurrentMonth ? 'pointer' : 'default'
            }}
          >
            {isCurrentMonth ? day : ''}
          </div>
        );
      })}
    </div>
  </div>
);

// Callout Component
export const Callout: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 80,
      position: 'relative'
    }}
  >
    {/* Callout Bubble */}
    <div
      style={{
        backgroundColor: component.backgroundColor || '#ffffff',
        border: `2px solid ${component.borderColor || '#3b82f6'}`,
        borderRadius: '12px',
        padding: '12px 16px',
        fontSize: component.fontSize || 14,
        color: component.textColor || '#374151',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        position: 'relative'
      }}
    >
      {component.text || 'Callout message'}
      {/* Callout Tail */}
      <div
        style={{
          position: 'absolute',
          bottom: '-8px',
          left: '20px',
          width: '0',
          height: '0',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderTop: `8px solid ${component.borderColor || '#3b82f6'}`
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: '-6px',
          left: '22px',
          width: '0',
          height: '0',
          borderLeft: '6px solid transparent',
          borderRight: '6px solid transparent',
          borderTop: `6px solid ${component.backgroundColor || '#ffffff'}`
        }}
      />
    </div>
  </div>
);

// Comment Component
export const Comment: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 250,
      height: component.height || 100,
      backgroundColor: component.backgroundColor || '#fef3c7',
      border: `1px solid ${component.borderColor || '#f59e0b'}`,
      borderRadius: '8px',
      padding: '12px',
      position: 'relative',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
    }}
  >
    {/* Comment Header */}
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginBottom: '8px'
      }}
    >
      <div
        style={{
          width: '24px',
          height: '24px',
          backgroundColor: '#f59e0b',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: '#ffffff',
          fontWeight: '500'
        }}
      >
        U
      </div>
      <div>
        <div style={{ fontSize: '12px', fontWeight: '500', color: '#92400e' }}>User</div>
        <div style={{ fontSize: '10px', color: '#a16207' }}>2 min ago</div>
      </div>
    </div>
    {/* Comment Text */}
    <div
      style={{
        fontSize: component.fontSize || 14,
        color: component.textColor || '#92400e',
        lineHeight: '1.4'
      }}
    >
      {component.text || 'This is a comment or annotation'}
    </div>
    {/* Comment Pin */}
    <div
      style={{
        position: 'absolute',
        top: '-4px',
        right: '12px',
        width: '8px',
        height: '8px',
        backgroundColor: '#ef4444',
        borderRadius: '50%',
        border: '2px solid #ffffff'
      }}
    />
  </div>
);

export const mediaComponents = {
  'Image Placeholder': ImagePlaceholder,
  'Video Placeholder': VideoPlaceholder,
  'Icon Placeholder': IconPlaceholder,
  'Avatar': Avatar,
  'Map Placeholder': MapPlaceholder,
  'Calendar': Calendar,
  'Callout': Callout,
  'Comment': Comment
};
