import React, { useRef, useState } from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

// Common components that are most frequently used in wireframes
// These are references to existing components from other categories

// Button (from forms)
export const CommonButton: React.FC<{ component: WireframeComponent }> = ({ component }) => {
  const borderRadius = component.borderRadius !== undefined
    ? (component.borderRadius === 9999 ? '50px' : `${component.borderRadius}px`)
    : '6px';

  return (
    <div
      style={{
        width: component.width || 100,
        height: component.height || 40,
        backgroundColor: component.backgroundColor || '#3b82f6',
        color: component.textColor || '#ffffff',
        border: `1px solid ${component.borderColor || '#2563eb'}`,
        borderRadius,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: component.fontSize || 14,
        fontWeight: '500',
        cursor: 'pointer',
        userSelect: 'none'
      }}
    >
      {component.text || 'Button'}
    </div>
  );
};

// Text Input (from forms)
export const CommonTextInput: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      padding: '8px 12px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151',
      display: 'flex',
      alignItems: 'center'
    }}
  >
    {component.text || 'Text input'}
  </div>
);

// Text Label (from text)
export const CommonTextLabel: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 100,
      height: component.height || 24,
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151',
      fontWeight: component.fontWeight || '400',
      display: 'flex',
      alignItems: 'center',
      userSelect: 'none'
    }}
  >
    {component.text || 'Label'}
  </div>
);

// Image (from media) - Enhanced with upload and URL support
export const CommonImage: React.FC<{ component: WireframeComponent }> = ({ component }) => {
  const [isUploading, setIsUploading] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        // Update component with image URL through the onUpdate callback if available
        if (component.onUpdate) {
          component.onUpdate({
            ...component,
            data: { ...component.data, imageUrl },
            text: file.name // Set filename as text
          });
        }
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUrlInput = () => {
    const url = prompt('Enter image URL:', component.data?.imageUrl || '');
    if (url !== null) { // Allow empty string to clear image
      // Update component with image URL
      if (component.onUpdate) {
        component.onUpdate({
          ...component,
          data: { ...component.data, imageUrl: url || undefined },
          text: url ? 'Image from URL' : 'Image'
        });
      }
    }
  };

  const hasImage = component.data?.imageUrl;

  if (hasImage) {
    return (
      <div
        style={{
          width: component.width || 200,
          height: component.height || 150,
          border: `1px solid ${component.borderColor || '#d1d5db'}`,
          borderRadius: '4px',
          overflow: 'hidden',
          position: 'relative',
          backgroundColor: component.backgroundColor || '#f3f4f6'
        }}
        onDoubleClick={() => fileInputRef.current?.click()}
        title="Double-click to change image"
      >
        <img
          src={component.data.imageUrl}
          alt={component.text || 'Image'}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
          onError={(e) => {
            // If image fails to load, show placeholder
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.parentElement!.innerHTML = `
              <div style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; color: #ef4444; font-size: 12px;">
                <div style="font-size: 24px; margin-bottom: 8px;">❌</div>
                <div>Failed to load image</div>
                <div style="font-size: 10px; margin-top: 4px;">Double-click to change</div>
              </div>
            `;
          }}
        />
        {/* Overlay controls when selected */}
        <div
          style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            display: 'flex',
            gap: '4px',
            opacity: 0.8
          }}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              fileInputRef.current?.click();
            }}
            style={{
              padding: '4px 6px',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              fontSize: '10px',
              cursor: 'pointer'
            }}
            title="Upload new image"
          >
            📁
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleUrlInput();
            }}
            style={{
              padding: '4px 6px',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              fontSize: '10px',
              cursor: 'pointer'
            }}
            title="Enter image URL"
          >
            🔗
          </button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </div>
    );
  }

  return (
    <div
      style={{
        width: component.width || 200,
        height: component.height || 150,
        backgroundColor: component.backgroundColor || '#f9fafb',
        border: `2px dashed ${component.borderColor || '#d1d5db'}`,
        borderRadius: '4px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: '#6b7280',
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer'
      }}
      onClick={() => fileInputRef.current?.click()}
      onDoubleClick={handleUrlInput}
      title="Click to upload image or double-click to enter URL"
    >
      {isUploading ? (
        <>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>⏳</div>
          <div>Uploading...</div>
        </>
      ) : (
        <>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>🖼️</div>
          <div style={{ fontWeight: '500' }}>{component.text || 'Click to Upload'}</div>
          <div style={{ fontSize: '10px', color: '#9ca3af', marginTop: '4px' }}>
            or Double-click for URL
          </div>
          <div style={{ fontSize: '10px', color: '#9ca3af', marginTop: '2px' }}>
            {component.width} × {component.height}
          </div>
        </>
      )}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        style={{ display: 'none' }}
      />
    </div>
  );
};

// Checkbox (from forms)
export const CommonCheckbox: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 120,
      height: component.height || 24,
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151'
    }}
  >
    <div
      style={{
        width: '16px',
        height: '16px',
        border: `1px solid ${component.borderColor || '#d1d5db'}`,
        borderRadius: '3px',
        backgroundColor: component.backgroundColor || '#ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '10px'
      }}
    >
      ✓
    </div>
    <span>{component.text || 'Checkbox'}</span>
  </div>
);

// Dropdown (from forms)
export const CommonDropdown: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 150,
      height: component.height || 40,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '6px',
      padding: '8px 12px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#374151',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      cursor: 'pointer'
    }}
  >
    <span>{component.text || 'Select option'}</span>
    <span style={{ fontSize: '12px', color: '#6b7280' }}>▼</span>
  </div>
);

// Link (from navigation)
export const CommonLink: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 80,
      height: component.height || 24,
      fontSize: component.fontSize || 14,
      color: component.textColor || '#3b82f6',
      textDecoration: 'underline',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      userSelect: 'none'
    }}
  >
    {component.text || 'Link'}
  </div>
);

// Icon (from media)
export const CommonIcon: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 32,
      height: component.height || 32,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 24,
      color: component.textColor || '#6b7280',
      backgroundColor: component.backgroundColor || 'transparent',
      borderRadius: '4px'
    }}
  >
    {component.text || '⭐'}
  </div>
);

export const commonComponents = {
  'Button': CommonButton,
  'Text Input': CommonTextInput,
  'Text Label': CommonTextLabel,
  'Text': CommonTextLabel, // Add "Text" as an alias for Text Label
  'Image': CommonImage,
  'Checkbox': CommonCheckbox,
  'Dropdown': CommonDropdown,
  'Link': CommonLink,
  'Icon': CommonIcon
};
