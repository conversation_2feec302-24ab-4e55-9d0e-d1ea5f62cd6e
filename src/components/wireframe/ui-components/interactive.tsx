import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || 'transparent',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#d1d5db'}`
        : defaultStyles.border || 'none',
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Tooltip
export const Tooltip: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: 'none'
    })}
  >
    <div style={{
      backgroundColor: component.backgroundColor || '#111827',
      color: component.textColor || '#ffffff',
      padding: '6px 12px',
      borderRadius: '6px',
      fontSize: component.fontSize || 12,
      position: 'relative',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    }}>
      {component.text || 'Tooltip text'}
      {/* Arrow */}
      <div style={{
        position: 'absolute',
        bottom: '-4px',
        left: '50%',
        transform: 'translateX(-50%)',
        width: 0,
        height: 0,
        borderLeft: '4px solid transparent',
        borderRight: '4px solid transparent',
        borderTop: `4px solid ${component.backgroundColor || '#111827'}`
      }}></div>
    </div>
  </div>
);

// Sticky Note
export const StickyNote: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      backgroundColor: component.backgroundColor || '#fef3c7',
      borderRadius: '4px',
      padding: '12px',
      color: component.textColor || '#92400e',
      position: 'relative',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      transform: 'rotate(-1deg)',
      border: 'none'
    })}
  >
    {/* Pin */}
    <div style={{
      position: 'absolute',
      top: '8px',
      right: '8px',
      width: '8px',
      height: '8px',
      backgroundColor: component.borderColor || '#dc2626',
      borderRadius: '50%',
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
    }}></div>
    {component.text || 'Sticky note content'}
  </div>
);

// Hotspot
export const Hotspot: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '2px dashed #ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      cursor: 'pointer'
    }}
  >
    <div style={{
      width: '12px',
      height: '12px',
      backgroundColor: '#ef4444',
      borderRadius: '50%',
      animation: 'pulse 2s infinite'
    }}></div>
    {component.text && (
      <div style={{
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        marginTop: '4px',
        fontSize: '10px',
        color: '#ef4444',
        whiteSpace: 'nowrap'
      }}>
        {component.text}
      </div>
    )}
  </div>
);

// Vertical Scrollbar
export const VerticalScrollbar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      backgroundColor: '#f3f4f6',
      borderRadius: '6px',
      position: 'relative',
      minWidth: '12px'
    }}
  >
    <div style={{
      width: '8px',
      height: '30%',
      backgroundColor: '#9ca3af',
      borderRadius: '4px',
      position: 'absolute',
      top: '20%',
      left: '50%',
      transform: 'translateX(-50%)'
    }}></div>
  </div>
);

// Horizontal Scrollbar
export const HorizontalScrollbar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      backgroundColor: '#f3f4f6',
      borderRadius: '6px',
      position: 'relative',
      minHeight: '12px'
    }}
  >
    <div style={{
      width: '30%',
      height: '8px',
      backgroundColor: '#9ca3af',
      borderRadius: '4px',
      position: 'absolute',
      left: '20%',
      top: '50%',
      transform: 'translateY(-50%)'
    }}></div>
  </div>
);

// Cursor Indicator Arrow
export const CursorArrow: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative'
    }}
  >
    <div style={{
      width: 0,
      height: 0,
      borderLeft: '8px solid #111827',
      borderTop: '8px solid transparent',
      borderBottom: '8px solid transparent',
      transform: 'rotate(-45deg)'
    }}></div>
    {component.text && (
      <div style={{
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        marginTop: '4px',
        fontSize: '10px',
        color: '#6b7280',
        whiteSpace: 'nowrap'
      }}>
        {component.text}
      </div>
    )}
  </div>
);

// Connector
export const Connector: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : 'none',
      position: 'relative',
      display: 'flex',
      alignItems: 'center'
    }}
  >
    <div style={{
      width: '100%',
      height: '2px',
      backgroundColor: '#6b7280',
      position: 'relative'
    }}>
      {/* Start dot */}
      <div style={{
        position: 'absolute',
        left: '-4px',
        top: '50%',
        transform: 'translateY(-50%)',
        width: '8px',
        height: '8px',
        backgroundColor: '#3b82f6',
        borderRadius: '50%'
      }}></div>
      {/* End arrow */}
      <div style={{
        position: 'absolute',
        right: '-6px',
        top: '50%',
        transform: 'translateY(-50%)',
        width: 0,
        height: 0,
        borderLeft: '6px solid #6b7280',
        borderTop: '3px solid transparent',
        borderBottom: '3px solid transparent'
      }}></div>
    </div>
    {component.text && (
      <div style={{
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        marginTop: '4px',
        fontSize: '10px',
        color: '#6b7280',
        whiteSpace: 'nowrap'
      }}>
        {component.text}
      </div>
    )}
  </div>
);

// Help Button Component
export const HelpButton: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 32,
      height: component.height || 32,
      backgroundColor: component.backgroundColor || '#3b82f6',
      color: component.textColor || '#ffffff',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 16,
      fontWeight: 'bold',
      cursor: 'pointer',
      border: `1px solid ${component.borderColor || '#2563eb'}`
    }}
  >
    ?
  </div>
);

// H Slider (Horizontal Slider) Component
export const HSlider: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 40,
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    }}
  >
    <div
      style={{
        flex: 1,
        height: '4px',
        backgroundColor: '#e5e7eb',
        borderRadius: '2px',
        position: 'relative'
      }}
    >
      {/* Slider Track */}
      <div
        style={{
          width: '40%',
          height: '100%',
          backgroundColor: component.backgroundColor || '#3b82f6',
          borderRadius: '2px'
        }}
      />
      {/* Slider Thumb */}
      <div
        style={{
          position: 'absolute',
          left: '40%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: '16px',
          height: '16px',
          backgroundColor: '#ffffff',
          border: `2px solid ${component.borderColor || '#3b82f6'}`,
          borderRadius: '50%',
          cursor: 'pointer'
        }}
      />
    </div>
    <span
      style={{
        fontSize: component.fontSize || 12,
        color: component.textColor || '#6b7280',
        minWidth: '30px'
      }}
    >
      {component.text || '40%'}
    </span>
  </div>
);

// iOS Keyboard Component
export const IOSKeyboard: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 375,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#d1d5db',
      borderRadius: '8px 8px 0 0',
      padding: '8px',
      display: 'grid',
      gridTemplateRows: 'repeat(4, 1fr)',
      gap: '4px'
    }}
  >
    {/* Row 1 */}
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(10, 1fr)', gap: '4px' }}>
      {['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'].map((key, index) => (
        <div
          key={index}
          style={{
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          {key}
        </div>
      ))}
    </div>
    {/* Row 2 */}
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(9, 1fr)', gap: '4px', paddingLeft: '20px' }}>
      {['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'].map((key, index) => (
        <div
          key={index}
          style={{
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          {key}
        </div>
      ))}
    </div>
    {/* Row 3 */}
    <div style={{ display: 'grid', gridTemplateColumns: '1.5fr repeat(7, 1fr) 1.5fr', gap: '4px' }}>
      <div style={{ backgroundColor: '#9ca3af', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>⇧</div>
      {['Z', 'X', 'C', 'V', 'B', 'N', 'M'].map((key, index) => (
        <div
          key={index}
          style={{
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          {key}
        </div>
      ))}
      <div style={{ backgroundColor: '#9ca3af', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>⌫</div>
    </div>
    {/* Row 4 */}
    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 3fr 1fr 1fr', gap: '4px' }}>
      <div style={{ backgroundColor: '#9ca3af', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>123</div>
      <div style={{ backgroundColor: '#9ca3af', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>🌐</div>
      <div style={{ backgroundColor: '#ffffff', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '14px' }}>space</div>
      <div style={{ backgroundColor: '#9ca3af', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>.</div>
      <div style={{ backgroundColor: '#3b82f6', color: '#ffffff', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>return</div>
    </div>
  </div>
);

// iOS Menu Component
export const IOSMenu: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 250,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      overflow: 'hidden',
      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
    }}
  >
    {['Copy', 'Cut', 'Paste', 'Share', 'Delete'].map((item, index) => (
      <div
        key={index}
        style={{
          padding: '16px 20px',
          fontSize: component.fontSize || 16,
          color: index === 4 ? '#ef4444' : component.textColor || '#374151',
          borderBottom: index < 4 ? '1px solid rgba(0, 0, 0, 0.1)' : 'none',
          cursor: 'pointer',
          textAlign: 'center'
        }}
      >
        {index === 0 && component.text ? component.text : item}
      </div>
    ))}
  </div>
);

// iOS Picker Component
export const IOSPicker: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 150,
      backgroundColor: component.backgroundColor || '#ffffff',
      border: `1px solid ${component.borderColor || '#d1d5db'}`,
      borderRadius: '8px',
      overflow: 'hidden',
      position: 'relative'
    }}
  >
    {/* Picker Columns */}
    <div style={{ display: 'flex', height: '100%' }}>
      {/* Hour Column */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '4px' }}>11</div>
        <div style={{ fontSize: component.fontSize || 18, fontWeight: '500', color: component.textColor || '#374151' }}>12</div>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>1</div>
      </div>
      {/* Minute Column */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '4px' }}>29</div>
        <div style={{ fontSize: component.fontSize || 18, fontWeight: '500', color: component.textColor || '#374151' }}>30</div>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>31</div>
      </div>
      {/* AM/PM Column */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '4px' }}>AM</div>
        <div style={{ fontSize: component.fontSize || 18, fontWeight: '500', color: component.textColor || '#374151' }}>PM</div>
        <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}></div>
      </div>
    </div>
    {/* Selection Indicator */}
    <div
      style={{
        position: 'absolute',
        top: '50%',
        left: '0',
        right: '0',
        height: '40px',
        transform: 'translateY(-50%)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        border: '1px solid rgba(59, 130, 246, 0.3)',
        pointerEvents: 'none'
      }}
    />
  </div>
);

// Cover Flow Component
export const CoverFlow: React.FC<{ component: WireframeComponent }> = ({ component }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 200,
      backgroundColor: component.backgroundColor || '#1f2937',
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '20px',
      padding: '20px',
      overflow: 'hidden'
    }}
  >
    {/* Left Cover */}
    <div
      style={{
        width: '80px',
        height: '120px',
        backgroundColor: '#374151',
        borderRadius: '4px',
        transform: 'perspective(200px) rotateY(45deg)',
        opacity: 0.7
      }}
    />
    {/* Center Cover */}
    <div
      style={{
        width: '100px',
        height: '150px',
        backgroundColor: '#ffffff',
        borderRadius: '4px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: '#6b7280',
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)'
      }}
    >
      {component.text || 'Cover'}
    </div>
    {/* Right Cover */}
    <div
      style={{
        width: '80px',
        height: '120px',
        backgroundColor: '#374151',
        borderRadius: '4px',
        transform: 'perspective(200px) rotateY(-45deg)',
        opacity: 0.7
      }}
    />
  </div>
);

// Red X Component
export const RedX: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 32,
      height: component.height || 32,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 24,
      color: component.textColor || '#ef4444',
      backgroundColor: component.backgroundColor || 'transparent',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      borderRadius: '50%',
      cursor: 'pointer',
      fontWeight: 'bold'
    }}
  >
    ✕
  </div>
);

// Scratch-O Component
export const ScratchO: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 100,
      height: component.height || 30,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 16,
      color: component.textColor || '#6b7280',
      backgroundColor: component.backgroundColor || 'transparent',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      position: 'relative'
    }}
  >
    <span style={{ textDecoration: 'line-through', opacity: 0.7 }}>
      {component.text || 'Scratched out text'}
    </span>
  </div>
);

// Shape Component
export const Shape: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const shapeType = component.data?.shapeType || 'circle';

  const getShapeStyle = () => {
    const baseStyle = {
      width: component.width || 100,
      height: component.height || 100,
      backgroundColor: component.backgroundColor || '#e5e7eb',
      border: isSelected ? '2px solid #3b82f6' : `2px solid ${component.borderColor || '#d1d5db'}`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    };

    switch (shapeType) {
      case 'circle':
        return { ...baseStyle, borderRadius: '50%' };
      case 'triangle':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          border: 'none',
          clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
          backgroundColor: component.backgroundColor || '#e5e7eb'
        };
      case 'diamond':
        return {
          ...baseStyle,
          transform: 'rotate(45deg)',
          borderRadius: '8px'
        };
      default:
        return baseStyle;
    }
  };

  return (
    <div style={getShapeStyle()}>
      {shapeType !== 'triangle' && (component.text || '')}
    </div>
  );
};

// Site Map Component
export const SiteMap: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#ffffff',
      padding: '16px',
      fontSize: component.fontSize || 12,
      color: component.textColor || '#374151'
    }}
  >
    <div style={{ textAlign: 'center', marginBottom: '12px', fontWeight: 'bold' }}>
      {component.text || 'Site Map'}
    </div>
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <div style={{ paddingLeft: '0px' }}>📄 Home</div>
      <div style={{ paddingLeft: '16px' }}>📄 About</div>
      <div style={{ paddingLeft: '16px' }}>📄 Services</div>
      <div style={{ paddingLeft: '32px' }}>📄 Web Design</div>
      <div style={{ paddingLeft: '32px' }}>📄 Development</div>
      <div style={{ paddingLeft: '16px' }}>📄 Contact</div>
    </div>
  </div>
);

// Smartphone Component
export const Smartphone: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 400,
      backgroundColor: '#1f2937',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      borderRadius: '24px',
      padding: '20px 12px',
      position: 'relative'
    }}
  >
    {/* Screen */}
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: component.backgroundColor || '#ffffff',
        borderRadius: '16px',
        border: '1px solid #374151',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Status bar */}
      <div
        style={{
          height: '24px',
          backgroundColor: '#f3f4f6',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 8px',
          fontSize: '10px',
          color: '#374151'
        }}
      >
        <span>9:41</span>
        <span>100%</span>
      </div>
      {/* Content area */}
      <div
        style={{
          height: 'calc(100% - 24px)',
          padding: '16px',
          fontSize: component.fontSize || 14,
          color: component.textColor || '#6b7280',
          textAlign: 'center'
        }}
      >
        {component.text || 'Mobile App Content'}
      </div>
    </div>
    {/* Home indicator */}
    <div
      style={{
        position: 'absolute',
        bottom: '8px',
        left: '50%',
        transform: 'translateX(-50%)',
        width: '40px',
        height: '4px',
        backgroundColor: '#9ca3af',
        borderRadius: '2px'
      }}
    />
  </div>
);

// Street Map Component
export const StreetMap: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 200,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#f0f9ff',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Map grid pattern */}
    <svg width="100%" height="100%" style={{ position: 'absolute' }}>
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e7ff" strokeWidth="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
    </svg>
    {/* Streets */}
    <div style={{
      position: 'absolute',
      top: '30%',
      left: '0',
      right: '0',
      height: '4px',
      backgroundColor: '#6b7280'
    }} />
    <div style={{
      position: 'absolute',
      top: '0',
      bottom: '0',
      left: '40%',
      width: '4px',
      backgroundColor: '#6b7280'
    }} />
    {/* Map pin */}
    <div style={{
      position: 'absolute',
      top: '25%',
      left: '35%',
      fontSize: '20px',
      color: '#ef4444'
    }}>
      📍
    </div>
    {/* Map label */}
    <div style={{
      position: 'absolute',
      bottom: '8px',
      right: '8px',
      fontSize: component.fontSize || 10,
      color: component.textColor || '#6b7280',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      padding: '2px 4px',
      borderRadius: '2px'
    }}>
      {component.text || 'Map'}
    </div>
  </div>
);

// Tab Bar Component
export const TabBar: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 50,
      backgroundColor: component.backgroundColor || '#f9fafb',
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      display: 'flex',
      alignItems: 'center'
    }}
  >
    {['Home', 'Search', 'Profile', 'Settings'].map((tab, index) => (
      <div
        key={index}
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '8px',
          cursor: 'pointer',
          backgroundColor: index === 0 ? '#ffffff' : 'transparent',
          borderRadius: '4px',
          margin: '4px'
        }}
      >
        <div style={{ fontSize: '16px', marginBottom: '2px' }}>
          {['🏠', '🔍', '👤', '⚙️'][index]}
        </div>
        <div style={{
          fontSize: component.fontSize || 10,
          color: index === 0 ? '#3b82f6' : component.textColor || '#6b7280',
          fontWeight: index === 0 ? '500' : '400'
        }}>
          {index === 0 && component.text ? component.text : tab}
        </div>
      </div>
    ))}
  </div>
);

// Tag Cloud Component
export const TagCloud: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const tags = component.data?.tags || ['React', 'JavaScript', 'CSS', 'HTML', 'Node.js', 'TypeScript', 'Design', 'UX'];

  return (
    <div
      style={{
        width: component.width || 300,
        height: component.height || 150,
        border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
        borderRadius: '4px',
        backgroundColor: component.backgroundColor || '#ffffff',
        padding: '16px',
        display: 'flex',
        flexWrap: 'wrap',
        gap: '8px',
        alignItems: 'flex-start',
        alignContent: 'flex-start'
      }}
    >
      {tags.map((tag, index) => (
        <span
          key={index}
          style={{
            padding: '4px 8px',
            backgroundColor: '#e5e7eb',
            borderRadius: '12px',
            fontSize: component.fontSize || (10 + Math.random() * 6),
            color: component.textColor || '#374151',
            cursor: 'pointer',
            userSelect: 'none'
          }}
        >
          {tag}
        </span>
      ))}
    </div>
  );
};

// Tree Pane Component
export const TreePane: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 250,
      height: component.height || 300,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#ffffff',
      padding: '12px',
      fontSize: component.fontSize || 12,
      color: component.textColor || '#374151',
      overflow: 'auto'
    }}
  >
    <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
      {component.text || 'File Explorer'}
    </div>
    <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
      <div style={{ paddingLeft: '0px', cursor: 'pointer' }}>📁 src</div>
      <div style={{ paddingLeft: '16px', cursor: 'pointer' }}>📁 components</div>
      <div style={{ paddingLeft: '32px', cursor: 'pointer' }}>📄 Button.tsx</div>
      <div style={{ paddingLeft: '32px', cursor: 'pointer' }}>📄 Input.tsx</div>
      <div style={{ paddingLeft: '16px', cursor: 'pointer' }}>📁 pages</div>
      <div style={{ paddingLeft: '32px', cursor: 'pointer' }}>📄 Home.tsx</div>
      <div style={{ paddingLeft: '32px', cursor: 'pointer' }}>📄 About.tsx</div>
      <div style={{ paddingLeft: '0px', cursor: 'pointer' }}>📄 package.json</div>
      <div style={{ paddingLeft: '0px', cursor: 'pointer' }}>📄 README.md</div>
    </div>
  </div>
);

// Video Player Component
export const VideoPlayer: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 400,
      height: component.height || 250,
      backgroundColor: '#000000',
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Video content area */}
    <div
      style={{
        width: '100%',
        height: 'calc(100% - 40px)',
        backgroundColor: '#1f2937',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '48px',
        color: '#9ca3af'
      }}
    >
      ▶️
    </div>
    {/* Video controls */}
    <div
      style={{
        height: '40px',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        display: 'flex',
        alignItems: 'center',
        padding: '0 12px',
        gap: '8px'
      }}
    >
      <button style={{
        backgroundColor: 'transparent',
        border: 'none',
        color: '#ffffff',
        fontSize: '16px',
        cursor: 'pointer'
      }}>⏸</button>
      <div style={{
        flex: 1,
        height: '4px',
        backgroundColor: '#374151',
        borderRadius: '2px',
        position: 'relative'
      }}>
        <div style={{
          width: '30%',
          height: '100%',
          backgroundColor: '#3b82f6',
          borderRadius: '2px'
        }} />
      </div>
      <span style={{
        color: '#ffffff',
        fontSize: component.fontSize || 12
      }}>
        {component.text || '1:23 / 4:56'}
      </span>
    </div>
  </div>
);

// Volume Slider Component
export const VolumeSlider: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 150,
      height: component.height || 30,
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      borderRadius: '4px',
      padding: '4px'
    }}
  >
    <div style={{ fontSize: '16px' }}>🔊</div>
    <div
      style={{
        flex: 1,
        height: '4px',
        backgroundColor: component.backgroundColor || '#e5e7eb',
        borderRadius: '2px',
        position: 'relative'
      }}
    >
      <div
        style={{
          width: '60%',
          height: '100%',
          backgroundColor: component.borderColor || '#3b82f6',
          borderRadius: '2px'
        }}
      />
      <div
        style={{
          width: '12px',
          height: '12px',
          backgroundColor: '#ffffff',
          border: `2px solid ${component.borderColor || '#3b82f6'}`,
          borderRadius: '50%',
          position: 'absolute',
          top: '-4px',
          left: '60%',
          transform: 'translateX(-50%)',
          cursor: 'pointer'
        }}
      />
    </div>
  </div>
);

// Webcam Component
export const Webcam: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 150,
      backgroundColor: '#1f2937',
      border: isSelected ? '2px solid #3b82f6' : '2px solid #374151',
      borderRadius: '8px',
      position: 'relative',
      overflow: 'hidden'
    }}
  >
    {/* Camera view */}
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#111827',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '48px',
        color: '#6b7280'
      }}
    >
      📹
    </div>
    {/* Recording indicator */}
    <div
      style={{
        position: 'absolute',
        top: '8px',
        right: '8px',
        width: '12px',
        height: '12px',
        backgroundColor: '#ef4444',
        borderRadius: '50%',
        animation: 'pulse 2s infinite'
      }}
    />
    {/* Camera label */}
    <div
      style={{
        position: 'absolute',
        bottom: '8px',
        left: '8px',
        fontSize: component.fontSize || 10,
        color: '#ffffff',
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        padding: '2px 6px',
        borderRadius: '4px'
      }}
    >
      {component.text || 'Camera'}
    </div>
  </div>
);

export const interactiveComponents = {
  'Tooltip': Tooltip,
  'Sticky Note': StickyNote,
  'Hotspot': Hotspot,
  'Vertical Scrollbar': VerticalScrollbar,
  'Horizontal Scrollbar': HorizontalScrollbar,
  'Cursor Arrow': CursorArrow,
  'Connector': Connector,
  'Help Button': HelpButton,
  'H Slider': HSlider,
  'iOS Keyboard': IOSKeyboard,
  'iOS Menu': IOSMenu,
  'iOS Picker': IOSPicker,
  'Cover Flow': CoverFlow,
  'Red X': RedX,
  'Scratch-O': ScratchO,
  'Shape': Shape,
  'Site Map': SiteMap,
  'Smartphone': Smartphone,
  'Street Map': StreetMap,
  'Tab Bar': TabBar,
  'Tag Cloud': TagCloud,
  'Tree Pane': TreePane,
  'Video Player': VideoPlayer,
  'Volume Slider': VolumeSlider,
  'Webcam': Webcam
};
