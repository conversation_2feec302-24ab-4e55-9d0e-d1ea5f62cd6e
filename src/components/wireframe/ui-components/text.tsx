import React from 'react';
import { WireframeComponent } from '../WireframePropertiesPanel';

interface ComponentProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Helper function to get component styling
const getComponentStyle = (component: WireframeComponent, isSelected: boolean, defaultStyles: React.CSSProperties = {}) => {
  return {
    width: component.width,
    height: component.height,
    fontSize: component.fontSize || defaultStyles.fontSize || 14,
    color: component.textColor || defaultStyles.color || '#374151',
    backgroundColor: component.backgroundColor || defaultStyles.backgroundColor || 'transparent',
    border: isSelected
      ? '2px solid #3b82f6'
      : component.borderWidth
        ? `${component.borderWidth}px solid ${component.borderColor || '#000000'}`
        : defaultStyles.border || '2px solid transparent',
    opacity: component.opacity !== undefined ? component.opacity / 100 : 1,
    ...defaultStyles
  };
};

// Text Label
export const TextLabel: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      display: 'flex',
      alignItems: 'center',
      fontWeight: '500',
      padding: '4px'
    })}
  >
    {component.text || 'Label Text'}
  </div>
);

// Text Paragraph
export const TextParagraph: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={getComponentStyle(component, isSelected, {
      padding: '8px',
      lineHeight: '1.5',
      overflow: 'hidden'
    })}
  >
    {component.text || 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.'}
  </div>
);

// Squiggly Line of Text (placeholder text)
export const SquigglyText: React.FC<ComponentProps> = ({ component, isSelected }) => {
  const lineColor = component.textColor || '#d1d5db';

  return (
    <div
      style={getComponentStyle(component, isSelected, {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: '4px',
        padding: '8px'
      })}
    >
      {/* Squiggly lines representing text */}
      <div style={{
        height: '3px',
        backgroundColor: lineColor,
        borderRadius: '2px',
        width: '100%'
      }}></div>
      <div style={{
        height: '3px',
        backgroundColor: lineColor,
        borderRadius: '2px',
        width: '85%'
      }}></div>
      <div style={{
        height: '3px',
        backgroundColor: lineColor,
        borderRadius: '2px',
        width: '92%'
      }}></div>
      <div style={{
        height: '3px',
        backgroundColor: lineColor,
        borderRadius: '2px',
        width: '78%'
      }}></div>
      {component.height > 60 && (
        <>
          <div style={{
            height: '3px',
            backgroundColor: lineColor,
            borderRadius: '2px',
            width: '95%'
          }}></div>
          <div style={{
            height: '3px',
            backgroundColor: lineColor,
            borderRadius: '2px',
            width: '70%'
          }}></div>
        </>
      )}
    </div>
  );
};

// Text Title Component
export const TextTitle: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 40,
      display: 'flex',
      alignItems: 'center',
      fontSize: component.fontSize || 24,
      fontWeight: 'bold',
      color: component.textColor || '#111827',
      backgroundColor: component.backgroundColor || 'transparent',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      padding: '4px'
    }}
  >
    {component.text || 'A Big Title'}
  </div>
);

// Text Subtitle Component
export const TextSubtitle: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 250,
      height: component.height || 30,
      display: 'flex',
      alignItems: 'center',
      fontSize: component.fontSize || 18,
      fontWeight: '600',
      color: component.textColor || '#374151',
      backgroundColor: component.backgroundColor || 'transparent',
      border: isSelected ? '2px solid #3b82f6' : 'none',
      padding: '4px'
    }}
  >
    {component.text || 'A Subtitle'}
  </div>
);

// Text Input Field Component (different from form input)
export const TextInputField: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 32,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#ffffff',
      display: 'flex',
      alignItems: 'center',
      padding: '0 8px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280'
    }}
  >
    {component.text || 'Text Input'}
  </div>
);

// Text Area Field Component (different from form textarea)
export const TextAreaField: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 300,
      height: component.height || 100,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: component.backgroundColor || '#ffffff',
      padding: '8px',
      fontSize: component.fontSize || 14,
      color: component.textColor || '#6b7280',
      overflow: 'hidden'
    }}
  >
    {component.text || 'Text Area\nMultiple lines of text\ncan go here...'}
  </div>
);

// Squiggly Line Component
export const SquigglyLine: React.FC<ComponentProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width || 200,
      height: component.height || 20,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: isSelected ? '2px solid #3b82f6' : 'none'
    }}
  >
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 200 20"
      style={{ overflow: 'visible' }}
    >
      <path
        d="M 0 10 Q 10 5 20 10 T 40 10 T 60 10 T 80 10 T 100 10 T 120 10 T 140 10 T 160 10 T 180 10 T 200 10"
        stroke={component.borderColor || '#6b7280'}
        strokeWidth="2"
        fill="none"
      />
    </svg>
  </div>
);

export const textComponents = {
  'Text Label': TextLabel,
  'Text Paragraph': TextParagraph,
  'Squiggly Text': SquigglyText,
  'Text Title': TextTitle,
  'Text Subtitle': TextSubtitle,
  'Text Input': TextInputField,
  'Text Area': TextAreaField,
  'Squiggly Line': SquigglyLine
};
