import React, { useEffect, useCallback } from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';

interface AccessibilityEnhancementsProps {
  components: WireframeComponent[];
  selectedComponent: WireframeComponent | null;
  onSelectComponent: (component: WireframeComponent) => void;
  onMoveComponent: (id: string, x: number, y: number) => void;
  gridSize?: number;
}

export const AccessibilityEnhancements: React.FC<AccessibilityEnhancementsProps> = ({
  components,
  selectedComponent,
  onSelectComponent,
  onMoveComponent,
  gridSize = 20
}) => {
  // Keyboard navigation for components
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!selectedComponent) return;

    // Don't handle keyboard events if user is typing in any text input
    const target = event.target as HTMLElement;
    if (target instanceof HTMLInputElement ||
        target instanceof HTMLTextAreaElement ||
        target instanceof HTMLSelectElement ||
        target?.contentEditable === 'true' ||
        target?.closest('[contenteditable="true"]') ||
        target?.closest('[role="dialog"]') ||
        target?.closest('[data-radix-dialog-content]') ||
        target?.closest('input') ||
        target?.closest('textarea') ||
        // Check if any input or textarea is currently focused
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement ||
        document.activeElement?.getAttribute('contenteditable') === 'true') {
      return;
    }

    const { key, shiftKey, ctrlKey, metaKey } = event;
    const isModifierPressed = ctrlKey || metaKey;
    const moveDistance = shiftKey ? gridSize : 1;

    switch (key) {
      case 'ArrowUp':
        event.preventDefault();
        onMoveComponent(
          selectedComponent.id,
          selectedComponent.x,
          Math.max(0, selectedComponent.y - moveDistance)
        );
        break;

      case 'ArrowDown':
        event.preventDefault();
        onMoveComponent(
          selectedComponent.id,
          selectedComponent.x,
          selectedComponent.y + moveDistance
        );
        break;

      case 'ArrowLeft':
        event.preventDefault();
        onMoveComponent(
          selectedComponent.id,
          Math.max(0, selectedComponent.x - moveDistance),
          selectedComponent.y
        );
        break;

      case 'ArrowRight':
        event.preventDefault();
        onMoveComponent(
          selectedComponent.id,
          selectedComponent.x + moveDistance,
          selectedComponent.y
        );
        break;

      case 'Tab':
        if (!isModifierPressed) {
          event.preventDefault();
          navigateToNextComponent(shiftKey);
        }
        break;

      case 'Enter':
        // Don't handle Enter in text inputs
        if (!isModifierPressed) {
          event.preventDefault();
          // Trigger edit mode or action
          announceComponentDetails(selectedComponent);
        }
        break;

      case ' ':
        // Never handle space bar globally - it's always for text input
        return;

      case 'Escape':
        event.preventDefault();
        // Clear selection
        onSelectComponent(null as any);
        break;
    }
  }, [selectedComponent, onMoveComponent, onSelectComponent, gridSize]);

  // Navigate between components using Tab
  const navigateToNextComponent = useCallback((reverse: boolean = false) => {
    if (components.length === 0) return;

    const currentIndex = selectedComponent 
      ? components.findIndex(c => c.id === selectedComponent.id)
      : -1;

    let nextIndex;
    if (reverse) {
      nextIndex = currentIndex <= 0 ? components.length - 1 : currentIndex - 1;
    } else {
      nextIndex = currentIndex >= components.length - 1 ? 0 : currentIndex + 1;
    }

    const nextComponent = components[nextIndex];
    if (nextComponent) {
      onSelectComponent(nextComponent);
      announceComponentSelection(nextComponent);
    }
  }, [components, selectedComponent, onSelectComponent]);

  // Screen reader announcements
  const announceComponentSelection = useCallback((component: WireframeComponent) => {
    const announcement = `Selected ${component.type} component at position ${component.x}, ${component.y}. ${
      component.text ? `Text: ${component.text}. ` : ''
    }Use arrow keys to move, Tab to navigate, Enter for details.`;
    
    announceToScreenReader(announcement);
  }, []);

  const announceComponentDetails = useCallback((component: WireframeComponent) => {
    const announcement = `${component.type} component details: Position X ${component.x}, Y ${component.y}. Size ${component.width} by ${component.height} pixels. ${
      component.text ? `Contains text: ${component.text}` : 'No text content'
    }`;
    
    announceToScreenReader(announcement);
  }, []);

  const announceToScreenReader = useCallback((message: string) => {
    // Create a temporary element for screen reader announcements
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.position = 'absolute';
    announcement.style.left = '-10000px';
    announcement.style.width = '1px';
    announcement.style.height = '1px';
    announcement.style.overflow = 'hidden';
    
    document.body.appendChild(announcement);
    announcement.textContent = message;
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Announce when selection changes
  useEffect(() => {
    if (selectedComponent) {
      announceComponentSelection(selectedComponent);
    }
  }, [selectedComponent, announceComponentSelection]);

  return null; // This component doesn't render anything visible
};

// Focus management hook
export const useFocusManagement = () => {
  const [focusedElementId, setFocusedElementId] = React.useState<string | null>(null);

  const focusElement = useCallback((elementId: string) => {
    const element = document.querySelector(`[data-component-id="${elementId}"]`) as HTMLElement;
    if (element) {
      element.focus();
      setFocusedElementId(elementId);
    }
  }, []);

  const clearFocus = useCallback(() => {
    setFocusedElementId(null);
  }, []);

  return {
    focusedElementId,
    focusElement,
    clearFocus
  };
};

// High contrast mode detection and support
export const useHighContrastMode = () => {
  const [isHighContrast, setIsHighContrast] = React.useState(false);

  useEffect(() => {
    const checkHighContrast = () => {
      // Check for Windows high contrast mode
      const isWindowsHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      
      // Check for forced colors (Windows high contrast)
      const isForcedColors = window.matchMedia('(forced-colors: active)').matches;
      
      setIsHighContrast(isWindowsHighContrast || isForcedColors);
    };

    checkHighContrast();

    // Listen for changes
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');
    const forcedColorsQuery = window.matchMedia('(forced-colors: active)');

    contrastQuery.addEventListener('change', checkHighContrast);
    forcedColorsQuery.addEventListener('change', checkHighContrast);

    return () => {
      contrastQuery.removeEventListener('change', checkHighContrast);
      forcedColorsQuery.removeEventListener('change', checkHighContrast);
    };
  }, []);

  return isHighContrast;
};

// Reduced motion detection
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// ARIA live region for dynamic announcements
export const AriaLiveRegion: React.FC = () => (
  <div
    id="wireframe-announcements"
    aria-live="polite"
    aria-atomic="true"
    style={{
      position: 'absolute',
      left: '-10000px',
      width: '1px',
      height: '1px',
      overflow: 'hidden'
    }}
  />
);

// Skip link for keyboard navigation
export const SkipLink: React.FC<{ targetId: string }> = ({ targetId }) => (
  <a
    href={`#${targetId}`}
    className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded"
  >
    Skip to wireframe canvas
  </a>
);
