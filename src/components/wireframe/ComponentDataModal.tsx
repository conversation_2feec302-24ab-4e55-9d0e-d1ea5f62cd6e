import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { WireframeComponent } from './WireframePropertiesPanel'; // Assuming this path is correct
import { PlusCircle, Trash2 } from 'lucide-react';
import { showError } from '@/utils/toast'; // Assuming you have a toast utility

interface ComponentDataModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  component: WireframeComponent | null;
  onSave: (updatedComponent: WireframeComponent) => void;
}

export const ComponentDataModal: React.FC<ComponentDataModalProps> = ({ isOpen, onOpenChange, component, onSave }) => {
  const [data, setData] = useState<Record<string, any>>({});
  const [newKey, setNewKey] = useState("");
  const [newValue, setNewValue] = useState("");

  useEffect(() => {
    if (component) {
      setData(component.data || {});
    } else {
      setData({}); // Reset if no component
    }
    setNewKey(""); // Reset new key/value inputs when component changes or modal opens/closes
    setNewValue("");
  }, [component, isOpen]); // Also reset on isOpen change to ensure fresh state if modal is re-opened for same component

  const handleDataChange = (key: string, value: any) => {
    setData((prevData) => ({ ...prevData, [key]: value }));
  };

  const handleAddNewField = () => {
    const trimmedKey = newKey.trim();
    if (!trimmedKey) {
      showError("Field key cannot be empty.");
      return;
    }
    if (trimmedKey in data) {
      showError(`Field key "${trimmedKey}" already exists.`);
      return;
    }
    setData((prevData) => ({ ...prevData, [trimmedKey]: newValue }));
    setNewKey("");
    setNewValue("");
  };

  const handleDeleteField = (keyToDelete: string) => {
    setData((prevData) => {
      const newData = { ...prevData };
      delete newData[keyToDelete];
      return newData;
    });
  };

  const handleSubmit = () => {
    if (component) {
      const updatedComponent = { ...component, data };
      onSave(updatedComponent);
      onOpenChange(false); // Close modal on save
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md"> {/* Adjusted width for better layout */}
        <DialogHeader>
          <DialogTitle>Edit Component Data</DialogTitle>
          <DialogDescription>
            Manage the data for the selected {component?.type || 'component'}.
            {component?.type === 'Navbar' && ' Edit navigation items, toggle avatar, and customize appearance.'}
            {component?.type === 'Sidebar' && ' Edit menu items and customize the sidebar content.'}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 space-y-3 max-h-[60vh] overflow-y-auto pr-2"> {/* Scrollable area for data fields */}
          {component && (
            <div className="space-y-4">
              {Object.entries(data).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor={`data-key-${key}`} className="text-sm font-medium">
                      {key}
                    </Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteField(key)}
                      className="text-red-500 hover:text-red-700 h-6 w-6"
                    >
                      <Trash2 size={12} />
                    </Button>
                  </div>

                  {/* Special handling for arrays */}
                  {Array.isArray(value) ? (
                    <div className="space-y-2">
                      {value.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Input
                            value={item.toString()}
                            onChange={(e) => {
                              const newArray = [...value];
                              newArray[index] = e.target.value;
                              handleDataChange(key, newArray);
                            }}
                            className="text-sm flex-1"
                            placeholder={`${key} item ${index + 1}`}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              const newArray = value.filter((_, i) => i !== index);
                              handleDataChange(key, newArray);
                            }}
                            className="text-red-500 hover:text-red-700 h-8 w-8"
                          >
                            <Trash2 size={12} />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newArray = [...value, `New ${key.slice(0, -1)}`]; // Remove 's' from end
                          handleDataChange(key, newArray);
                        }}
                        className="text-xs w-full"
                      >
                        Add {key.slice(0, -1)} {/* Remove 's' from end */}
                      </Button>
                    </div>
                  ) : (
                    /* Regular input for non-array values */
                    <Input
                      id={`data-value-${key}`}
                      value={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                      onChange={(e) => {
                        // Attempt to parse if it looks like JSON, otherwise store as string
                        let parsedValue = e.target.value;
                        try {
                          if ((e.target.value.startsWith('{') && e.target.value.endsWith('}')) || (e.target.value.startsWith('[') && e.target.value.endsWith(']'))) {
                            parsedValue = JSON.parse(e.target.value);
                          }
                        } catch (err) { /* Keep as string if parse fails */ }
                        handleDataChange(key, parsedValue);
                      }}
                      className="text-sm"
                      placeholder={`Enter ${key}`}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
          
          {/* Quick presets for common component types */}
          {component && (component.type === 'Navbar' || component.type === 'Sidebar') && (
            <div className="pt-4 mt-4 border-t">
              <Label className="text-sm font-semibold block mb-2">Quick Actions</Label>
              <div className="flex gap-2 flex-wrap">
                {component.type === 'Navbar' && (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const currentItems = data.navItems || ['Home', 'About', 'Services', 'Contact'];
                        setData(prev => ({ ...prev, navItems: [...currentItems, 'New Item'] }));
                      }}
                      className="text-xs"
                    >
                      Add Nav Item
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setData(prev => ({ ...prev, showAvatar: !prev.showAvatar }))}
                      className="text-xs"
                    >
                      Toggle Avatar
                    </Button>
                  </>
                )}
                {component.type === 'Sidebar' && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const currentItems = data.menuItems || ['Dashboard', 'Projects', 'Tasks', 'Reports', 'Settings'];
                      setData(prev => ({ ...prev, menuItems: [...currentItems, 'New Menu Item'] }));
                    }}
                    className="text-xs"
                  >
                    Add Menu Item
                  </Button>
                )}
              </div>
            </div>
          )}

          <div className="pt-4 mt-4 border-t">
            <Label className="text-sm font-semibold block mb-2">Add New Field</Label>
            <div className="grid grid-cols-[1fr,2fr,auto] items-center gap-x-2 gap-y-2">
              <Input
                placeholder="Key"
                value={newKey}
                onChange={(e) => setNewKey(e.target.value)}
                className="text-sm"
              />
              <Input
                placeholder="Value (JSON or string)"
                value={newValue}
                onChange={(e) => setNewValue(e.target.value)}
                className="text-sm"
              />
              <Button
                type="button"
                onClick={handleAddNewField}
                variant="outline"
                size="icon"
                className="text-green-600 hover:text-green-700 border-green-600 hover:bg-green-50 h-8 w-8"
              >
                <PlusCircle size={18} />
              </Button>
            </div>
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          <Button type="button" className="primary-button" onClick={handleSubmit}>
            Save Data
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};