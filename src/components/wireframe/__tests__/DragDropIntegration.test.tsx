import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { WireframeToolbar } from '../WireframeToolbar';

// Mock component for testing
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <DndProvider backend={HTML5Backend}>
    {children}
  </DndProvider>
);

describe('Drag and Drop Integration', () => {
  test('toolbar components are draggable', () => {
    const mockAddComponent = jest.fn();
    
    render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    // Check that Button component is rendered and draggable
    const buttonComponent = screen.getByText('Button');
    expect(buttonComponent).toBeInTheDocument();
    
    // Check that component has proper title attribute
    expect(buttonComponent.closest('div')).toHaveAttribute('title', expect.stringContaining('Button'));
  });

  test('clicking toolbar component calls onAddComponent', () => {
    const mockAddComponent = jest.fn();
    
    render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    const buttonComponent = screen.getByText('Button');
    fireEvent.click(buttonComponent);
    
    expect(mockAddComponent).toHaveBeenCalledWith('Button');
  });

  test('toolbar shows different component categories', () => {
    const mockAddComponent = jest.fn();
    
    render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    // Check category filters
    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Layouts')).toBeInTheDocument();
    expect(screen.getByText('Form Controls')).toBeInTheDocument();
    expect(screen.getByText('Navigation')).toBeInTheDocument();
  });

  test('filtering by category shows correct components', () => {
    const mockAddComponent = jest.fn();
    
    render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    // Click on Form Controls category
    fireEvent.click(screen.getByText('Form Controls'));

    // Should show form control components
    expect(screen.getByText('Button')).toBeInTheDocument();
    expect(screen.getByText('Text Input')).toBeInTheDocument();
    
    // Should not show layout components
    expect(screen.queryByText('Desktop Browser')).not.toBeInTheDocument();
  });

  test('components have proper drag attributes', () => {
    const mockAddComponent = jest.fn();
    
    const { container } = render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    // Find draggable elements
    const draggableElements = container.querySelectorAll('[draggable="true"]');
    expect(draggableElements.length).toBeGreaterThan(0);
  });

  test('toolbar components show icons and compact layout', () => {
    const mockAddComponent = jest.fn();

    render(
      <TestWrapper>
        <WireframeToolbar onAddComponent={mockAddComponent} />
      </TestWrapper>
    );

    // Check that components have icons (emojis)
    const buttonComponent = screen.getByText('Button');
    const buttonContainer = buttonComponent.closest('div');
    expect(buttonContainer).toBeInTheDocument();

    // Check for truncated names in categories
    fireEvent.click(screen.getByText(/Media.*/));

    // Should show compact components
    expect(screen.getByText('Image...')).toBeInTheDocument();
  });
});
