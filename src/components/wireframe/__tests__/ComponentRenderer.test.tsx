import React from 'react';
import { render, screen } from '@testing-library/react';
import { ComponentRenderer, getAllComponentTypes, getComponentsByCategory } from '../ComponentRenderer';
import { WireframeComponent } from '../WireframePropertiesPanel';

// Mock component for testing
const mockComponent: WireframeComponent = {
  id: 'test-1',
  type: 'Button',
  x: 100,
  y: 100,
  width: 120,
  height: 40,
  text: 'Test Button',
  data: {}
};

describe('ComponentRenderer', () => {
  test('renders Button component correctly', () => {
    render(<ComponentRenderer component={mockComponent} isSelected={false} />);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  test('applies selection styles when selected', () => {
    const { container } = render(
      <ComponentRenderer component={mockComponent} isSelected={true} />
    );
    
    const buttonElement = container.firstChild as HTMLElement;
    expect(buttonElement).toHaveStyle({
      border: '2px solid #3b82f6'
    });
  });

  test('renders fallback component for unknown type', () => {
    const unknownComponent = { ...mockComponent, type: 'UnknownType' };
    render(<ComponentRenderer component={unknownComponent} isSelected={false} />);
    expect(screen.getByText('UnknownType')).toBeInTheDocument();
  });

  test('getAllComponentTypes returns all component types', () => {
    const types = getAllComponentTypes();
    expect(types).toContain('Button');
    expect(types).toContain('Text Input');
    expect(types).toContain('Navbar');
    expect(types.length).toBeGreaterThan(30); // We have 39 components
  });

  test('getComponentsByCategory returns organized categories', () => {
    const categories = getComponentsByCategory();
    expect(categories).toHaveProperty('Layouts');
    expect(categories).toHaveProperty('Form Controls');
    expect(categories).toHaveProperty('Navigation');
    expect(categories['Form Controls']).toContain('Button');
    expect(categories['Layouts']).toContain('Desktop Browser');
  });
});

describe('UI Components', () => {
  test('renders Text Input with placeholder', () => {
    const textInputComponent = {
      ...mockComponent,
      type: 'Text Input',
      text: 'Enter your name...'
    };
    
    render(<ComponentRenderer component={textInputComponent} isSelected={false} />);
    expect(screen.getByText('Enter your name...')).toBeInTheDocument();
  });

  test('renders Navbar with brand text', () => {
    const navbarComponent = {
      ...mockComponent,
      type: 'Navbar',
      text: 'My Brand',
      width: 800,
      height: 60
    };
    
    render(<ComponentRenderer component={navbarComponent} isSelected={false} />);
    expect(screen.getByText('My Brand')).toBeInTheDocument();
    expect(screen.getByText('Home')).toBeInTheDocument();
  });

  test('renders Table with headers and data', () => {
    const tableComponent = {
      ...mockComponent,
      type: 'Table',
      width: 400,
      height: 200
    };
    
    render(<ComponentRenderer component={tableComponent} isSelected={false} />);
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  test('renders Modal with close button', () => {
    const modalComponent = {
      ...mockComponent,
      type: 'Modal',
      text: 'Confirmation Dialog',
      width: 400,
      height: 300
    };
    
    render(<ComponentRenderer component={modalComponent} isSelected={false} />);
    expect(screen.getByText('Confirmation Dialog')).toBeInTheDocument();
    expect(screen.getByText('×')).toBeInTheDocument();
  });
});
