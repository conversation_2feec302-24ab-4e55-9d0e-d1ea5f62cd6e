import React from 'react';
import { render } from '@testing-library/react';
import { GridOverlay, snapToGrid, snapPointToGrid, snapRectToGrid } from '../GridOverlay';

describe('GridOverlay', () => {
  test('renders grid when visible', () => {
    const { container } = render(
      <GridOverlay 
        gridSize={20} 
        visible={true} 
        zoom={1} 
        canvasWidth={1000} 
        canvasHeight={1000} 
      />
    );
    
    const gridElement = container.firstChild as HTMLElement;
    expect(gridElement).toHaveStyle({
      backgroundSize: '20px 20px'
    });
  });

  test('does not render when not visible', () => {
    const { container } = render(
      <GridOverlay 
        gridSize={20} 
        visible={false} 
        zoom={1} 
        canvasWidth={1000} 
        canvasHeight={1000} 
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('adjusts grid size based on zoom', () => {
    const { container } = render(
      <GridOverlay 
        gridSize={20} 
        visible={true} 
        zoom={2} 
        canvasWidth={1000} 
        canvasHeight={1000} 
      />
    );
    
    const gridElement = container.firstChild as HTMLElement;
    expect(gridElement).toHaveStyle({
      backgroundSize: '40px 40px'
    });
  });

  test('hides grid when too small', () => {
    const { container } = render(
      <GridOverlay 
        gridSize={20} 
        visible={true} 
        zoom={0.1} 
        canvasWidth={1000} 
        canvasHeight={1000} 
      />
    );
    
    expect(container.firstChild).toBeNull();
  });
});

describe('Grid Snapping Functions', () => {
  describe('snapToGrid', () => {
    test('snaps values to grid correctly', () => {
      expect(snapToGrid(0, 20)).toBe(0);
      expect(snapToGrid(10, 20)).toBe(0);
      expect(snapToGrid(11, 20)).toBe(20);
      expect(snapToGrid(25, 20)).toBe(20);
      expect(snapToGrid(35, 20)).toBe(40);
    });

    test('works with different grid sizes', () => {
      expect(snapToGrid(15, 10)).toBe(20);
      expect(snapToGrid(15, 5)).toBe(15);
      expect(snapToGrid(17, 5)).toBe(15);
      expect(snapToGrid(18, 5)).toBe(20);
    });

    test('handles negative values', () => {
      expect(snapToGrid(-5, 20)).toBe(0);
      expect(snapToGrid(-15, 20)).toBe(-20);
      expect(snapToGrid(-25, 20)).toBe(-20);
    });
  });

  describe('snapPointToGrid', () => {
    test('snaps point coordinates to grid', () => {
      const point = { x: 15, y: 25 };
      const snapped = snapPointToGrid(point, 20);
      
      expect(snapped).toEqual({ x: 20, y: 20 });
    });

    test('works with different grid sizes', () => {
      const point = { x: 17, y: 23 };
      const snapped = snapPointToGrid(point, 10);
      
      expect(snapped).toEqual({ x: 20, y: 20 });
    });
  });

  describe('snapRectToGrid', () => {
    test('snaps rectangle to grid', () => {
      const rect = { x: 15, y: 25, width: 85, height: 45 };
      const snapped = snapRectToGrid(rect, 20);
      
      expect(snapped).toEqual({
        x: 20,
        y: 20,
        width: 80,
        height: 40
      });
    });

    test('enforces minimum size', () => {
      const rect = { x: 15, y: 25, width: 5, height: 5 };
      const snapped = snapRectToGrid(rect, 20);
      
      expect(snapped).toEqual({
        x: 20,
        y: 20,
        width: 20,
        height: 20
      });
    });

    test('works with different grid sizes', () => {
      const rect = { x: 17, y: 23, width: 33, height: 27 };
      const snapped = snapRectToGrid(rect, 10);
      
      expect(snapped).toEqual({
        x: 20,
        y: 20,
        width: 30,
        height: 30
      });
    });
  });
});
