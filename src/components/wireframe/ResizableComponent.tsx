import React, { useState, useCallback, useRef, useEffect } from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';
import { ComponentRenderer } from './ComponentRenderer';

interface ResizableComponentProps {
  component: WireframeComponent;
  isSelected: boolean;
  onSelect: (component: WireframeComponent) => void;
  onDoubleClick: (component: WireframeComponent) => void;
  onResize: (id: string, width: number, height: number) => void;
  onMove: (id: string, x: number, y: number) => void;
  onUpdate?: (component: WireframeComponent) => void;
  onDragStart?: () => void;
  onDragEnd?: () => void;
  gridSnapping?: boolean;
  gridSize?: number;
  zIndex: number;
}

interface ResizeHandle {
  position: 'nw' | 'n' | 'ne' | 'e' | 'se' | 's' | 'sw' | 'w';
  cursor: string;
}

const resizeHandles: ResizeHandle[] = [
  { position: 'nw', cursor: 'nw-resize' },
  { position: 'n', cursor: 'n-resize' },
  { position: 'ne', cursor: 'ne-resize' },
  { position: 'e', cursor: 'e-resize' },
  { position: 'se', cursor: 'se-resize' },
  { position: 's', cursor: 's-resize' },
  { position: 'sw', cursor: 'sw-resize' },
  { position: 'w', cursor: 'w-resize' }
];

export const ResizableComponent: React.FC<ResizableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onDoubleClick,
  onResize,
  onMove,
  onUpdate,
  onDragStart,
  onDragEnd,
  gridSnapping = true,
  gridSize = 20,
  zIndex
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string | null>(null);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [initialSize, setInitialSize] = useState({ width: 0, height: 0 });
  const [initialPosition, setInitialPosition] = useState({ x: 0, y: 0 });

  const componentRef = useRef<HTMLDivElement>(null);

  const snapToGrid = useCallback((value: number) => {
    if (!gridSnapping) return value;
    return Math.round(value / gridSize) * gridSize;
  }, [gridSnapping, gridSize]);

  const handleMouseDown = useCallback((e: React.MouseEvent, handle?: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (handle) {
      // Resize operation
      setIsResizing(true);
      setResizeHandle(handle);
      setDragStart({ x: e.clientX, y: e.clientY });
      setInitialSize({ width: component.width, height: component.height });
      setInitialPosition({ x: component.x, y: component.y });
    } else {
      // Move operation
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
      setInitialPosition({ x: component.x, y: component.y });
      // Show grid when starting to drag
      onDragStart?.();
    }

    onSelect(component);
  }, [component, onSelect, onDragStart]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing && !isDragging) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    if (isResizing && resizeHandle) {
      let newWidth = initialSize.width;
      let newHeight = initialSize.height;
      let newX = initialPosition.x;
      let newY = initialPosition.y;

      // Calculate new dimensions based on resize handle
      switch (resizeHandle) {
        case 'nw':
          newWidth = Math.max(20, initialSize.width - deltaX);
          newHeight = Math.max(20, initialSize.height - deltaY);
          newX = initialPosition.x + (initialSize.width - newWidth);
          newY = initialPosition.y + (initialSize.height - newHeight);
          break;
        case 'n':
          newHeight = Math.max(20, initialSize.height - deltaY);
          newY = initialPosition.y + (initialSize.height - newHeight);
          break;
        case 'ne':
          newWidth = Math.max(20, initialSize.width + deltaX);
          newHeight = Math.max(20, initialSize.height - deltaY);
          newY = initialPosition.y + (initialSize.height - newHeight);
          break;
        case 'e':
          newWidth = Math.max(20, initialSize.width + deltaX);
          break;
        case 'se':
          newWidth = Math.max(20, initialSize.width + deltaX);
          newHeight = Math.max(20, initialSize.height + deltaY);
          break;
        case 's':
          newHeight = Math.max(20, initialSize.height + deltaY);
          break;
        case 'sw':
          newWidth = Math.max(20, initialSize.width - deltaX);
          newHeight = Math.max(20, initialSize.height + deltaY);
          newX = initialPosition.x + (initialSize.width - newWidth);
          break;
        case 'w':
          newWidth = Math.max(20, initialSize.width - deltaX);
          newX = initialPosition.x + (initialSize.width - newWidth);
          break;
      }

      // Apply grid snapping only if enabled
      if (gridSnapping) {
        newWidth = snapToGrid(newWidth);
        newHeight = snapToGrid(newHeight);
        newX = snapToGrid(newX);
        newY = snapToGrid(newY);
      }

      onResize(component.id, newWidth, newHeight);
      if (newX !== component.x || newY !== component.y) {
        onMove(component.id, newX, newY);
      }
    } else if (isDragging) {
      // Move operation - smooth movement by default
      let newX = initialPosition.x + deltaX;
      let newY = initialPosition.y + deltaY;

      // Apply grid snapping only if enabled
      if (gridSnapping) {
        newX = snapToGrid(newX);
        newY = snapToGrid(newY);
      }

      // Ensure component stays within bounds
      newX = Math.max(0, newX);
      newY = Math.max(0, newY);

      onMove(component.id, newX, newY);
    }
  }, [
    isResizing,
    isDragging,
    resizeHandle,
    dragStart,
    initialSize,
    initialPosition,
    component.id,
    component.x,
    component.y,
    onResize,
    onMove,
    snapToGrid,
    gridSnapping
  ]);

  const handleMouseUp = useCallback(() => {
    const wasDragging = isDragging;
    setIsResizing(false);
    setIsDragging(false);
    setResizeHandle(null);

    // Hide grid when drag ends
    if (wasDragging) {
      onDragEnd?.();
    }
  }, [isDragging, onDragEnd]);

  // Handle keyboard movement - only when this specific component has focus
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Only handle arrow keys for movement and only when this component is focused
    if (!['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
      return;
    }

    // Don't handle if any text input is focused anywhere on the page
    const activeElement = document.activeElement;
    if (activeElement instanceof HTMLInputElement ||
        activeElement instanceof HTMLTextAreaElement ||
        activeElement instanceof HTMLSelectElement ||
        activeElement?.getAttribute('contenteditable') === 'true') {
      return;
    }

    // Only proceed if this component is actually focused
    if (document.activeElement !== componentRef.current) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    const moveDistance = e.shiftKey ? gridSize : 1; // Larger steps with Shift
    let newX = component.x;
    let newY = component.y;

    switch (e.key) {
      case 'ArrowLeft':
        newX = Math.max(0, component.x - moveDistance);
        break;
      case 'ArrowRight':
        newX = component.x + moveDistance;
        break;
      case 'ArrowUp':
        newY = Math.max(0, component.y - moveDistance);
        break;
      case 'ArrowDown':
        newY = component.y + moveDistance;
        break;
    }

    // Apply grid snapping if enabled
    if (gridSnapping) {
      newX = snapToGrid(newX);
      newY = snapToGrid(newY);
    }

    onMove(component.id, newX, newY);
  }, [component.x, component.y, component.id, gridSize, gridSnapping, snapToGrid, onMove]);

  // Add global mouse event listeners
  useEffect(() => {
    if (isResizing || isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = isResizing ? (resizeHandles.find(h => h.position === resizeHandle)?.cursor || 'default') : 'grabbing';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'default';
        document.body.style.userSelect = 'auto';
      };
    }
  }, [isResizing, isDragging, handleMouseMove, handleMouseUp, resizeHandle]);

  // No global keyboard listeners needed - using onKeyDown on the component directly

  const getHandleStyle = (handle: ResizeHandle) => {
    const baseStyle = {
      position: 'absolute' as const,
      width: '8px',
      height: '8px',
      backgroundColor: '#3b82f6',
      border: '1px solid #ffffff',
      borderRadius: '2px',
      cursor: handle.cursor,
      zIndex: 1000
    };

    switch (handle.position) {
      case 'nw': return { ...baseStyle, top: '-4px', left: '-4px' };
      case 'n': return { ...baseStyle, top: '-4px', left: '50%', transform: 'translateX(-50%)' };
      case 'ne': return { ...baseStyle, top: '-4px', right: '-4px' };
      case 'e': return { ...baseStyle, top: '50%', right: '-4px', transform: 'translateY(-50%)' };
      case 'se': return { ...baseStyle, bottom: '-4px', right: '-4px' };
      case 's': return { ...baseStyle, bottom: '-4px', left: '50%', transform: 'translateX(-50%)' };
      case 'sw': return { ...baseStyle, bottom: '-4px', left: '-4px' };
      case 'w': return { ...baseStyle, top: '50%', left: '-4px', transform: 'translateY(-50%)' };
      default: return baseStyle;
    }
  };

  // Debug: Only log if there are positioning issues
  if (component.x < 0 || component.y < 0 || component.width <= 0 || component.height <= 0) {
    console.warn('ResizableComponent - Invalid dimensions:', {
      componentId: component.id,
      componentType: component.type,
      position: { x: component.x, y: component.y },
      size: { width: component.width, height: component.height }
    });
  }

  return (
    <div
      ref={componentRef}
      tabIndex={isSelected ? 0 : -1}
      style={{
        position: 'absolute',
        left: component.x,
        top: component.y,
        width: component.width,
        height: component.height,
        zIndex: zIndex,
        cursor: isDragging ? 'grabbing' : 'grab',
        outline: 'none' // Remove default focus outline since we have custom selection border
      }}
      onMouseDown={(e) => handleMouseDown(e)}
      onKeyDown={isSelected ? handleKeyDown : undefined}
      onClick={(e) => {
        e.stopPropagation();
        onSelect(component);
        // Focus the component when selected for keyboard navigation
        if (componentRef.current) {
          componentRef.current.focus();
        }
      }}
      onDoubleClick={(e) => {
        e.stopPropagation();
        onDoubleClick(component);
      }}
    >
      {/* Component Content */}
      <div style={{ width: '100%', height: '100%', pointerEvents: 'none' }}>
        <ComponentRenderer
          component={{
            ...component,
            onUpdate: onUpdate
          }}
          isSelected={isSelected}
        />
      </div>

      {/* Selection Border */}
      {isSelected && (
        <div
          style={{
            position: 'absolute',
            inset: 0,
            border: '2px solid #3b82f6',
            borderRadius: '4px',
            pointerEvents: 'none',
            boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.2)'
          }}
        />
      )}

      {/* Resize Handles */}
      {isSelected && resizeHandles.map((handle) => (
        <div
          key={handle.position}
          style={getHandleStyle(handle)}
          onMouseDown={(e) => handleMouseDown(e, handle.position)}
        />
      ))}
    </div>
  );
};
