import React from 'react';

interface GridOverlayProps {
  gridSize?: number;
  visible?: boolean;
  zoom?: number;
  canvasWidth?: number;
  canvasHeight?: number;
  isDragging?: boolean; // New prop to indicate if something is being dragged
}

export const GridOverlay: React.FC<GridOverlayProps> = ({
  gridSize = 20,
  visible = true,
  zoom = 1,
  canvasWidth = 2000,
  canvasHeight = 2000,
  isDragging = false
}) => {
  if (!visible) return null;

  const adjustedGridSize = gridSize * zoom;

  // Don't show grid if it's too small (would be too dense)
  if (adjustedGridSize < 8) return null;

  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        backgroundImage: `
          linear-gradient(to right, rgba(156, 163, 175, ${isDragging ? '0.4' : '0.2'}) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(156, 163, 175, ${isDragging ? '0.4' : '0.2'}) 1px, transparent 1px)
        `,
        backgroundSize: `${adjustedGridSize}px ${adjustedGridSize}px`,
        width: canvasWidth * zoom,
        height: canvasHeight * zoom,
        zIndex: isDragging ? 99999 : 1, // Highest z-index when dragging
        opacity: isDragging ? 1 : 0.7
      }}
    />
  );
};

// Grid snapping utility functions
export const snapToGrid = (value: number, gridSize: number = 20): number => {
  return Math.round(value / gridSize) * gridSize;
};

export const snapPointToGrid = (point: { x: number; y: number }, gridSize: number = 20) => {
  return {
    x: snapToGrid(point.x, gridSize),
    y: snapToGrid(point.y, gridSize)
  };
};

export const snapRectToGrid = (
  rect: { x: number; y: number; width: number; height: number }, 
  gridSize: number = 20
) => {
  return {
    x: snapToGrid(rect.x, gridSize),
    y: snapToGrid(rect.y, gridSize),
    width: Math.max(gridSize, snapToGrid(rect.width, gridSize)),
    height: Math.max(gridSize, snapToGrid(rect.height, gridSize))
  };
};
