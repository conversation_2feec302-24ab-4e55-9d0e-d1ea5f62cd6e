import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { PlusCircle, Trash2, GripVertical } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { useDrag, useDrop } from 'react-dnd';

export interface WireframePageItem {
  id: string;
  name: string;
}

interface WireframePagesPanelProps {
  pages: WireframePageItem[];
  activePageId: string | null;
  onAddPage: (pageName: string) => void;
  onDeletePage: (pageId: string) => void;
  onSelectPage: (pageId: string) => void;
  onUpdatePageName: (pageId: string, newName: string) => void;
  onReorderPages: (reorderedPages: WireframePageItem[]) => void;
}

// Draggable Page Item Component
const DraggablePageItem: React.FC<{
  page: WireframePageItem;
  index: number;
  isActive: boolean;
  isEditing: boolean;
  tempName: string;
  onSelect: () => void;
  onDelete: () => void;
  onStartEdit: () => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onTempNameChange: (name: string) => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
}> = ({
  page,
  index,
  isActive,
  isEditing,
  tempName,
  onSelect,
  onDelete,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  onTempNameChange,
  onMove
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'PAGE',
    item: { id: page.id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'PAGE',
    hover: (item: { id: string; index: number }) => {
      if (item.index !== index) {
        onMove(item.index, index);
        item.index = index;
      }
    },
  });

  return (
    <li
      ref={(node) => drag(drop(node))}
      className={cn(
        "p-2 rounded-md cursor-pointer text-sm text-gray-600 flex items-center gap-2 transition-opacity",
        isActive ? "bg-creative-blue-lighter-bg text-creative-blue font-medium" : "hover:bg-creative-blue-lighter-bg",
        isDragging ? "opacity-50" : "opacity-100"
      )}
      onClick={onSelect}
    >
      <GripVertical size={14} className="text-gray-400 cursor-grab" />
      <div className="flex-1 flex justify-between items-center">
        {isEditing ? (
          <Input
            value={tempName}
            onChange={(e) => onTempNameChange(e.target.value)}
            onBlur={onSaveEdit}
            onKeyDown={(e) => {
              if (e.key === 'Enter') onSaveEdit();
              if (e.key === 'Escape') onCancelEdit();
            }}
            autoFocus
            className="h-8 text-sm"
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <span onDoubleClick={onStartEdit} className="flex-1 pr-2">
            {page.name}
          </span>
        )}
        <div className="space-x-1">
          {isEditing ? (
            <>
              <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700" onClick={(e) => { e.stopPropagation(); onSaveEdit(); }}>
                Save
              </Button>
              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" onClick={(e) => { e.stopPropagation(); onCancelEdit(); }}>
                Cancel
              </Button>
            </>
          ) : (
            <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" onClick={(e) => { e.stopPropagation(); onDelete(); }}>
              <Trash2 size={16} />
            </Button>
          )}
        </div>
      </div>
    </li>
  );
};

export const WireframePagesPanel: React.FC<WireframePagesPanelProps> = ({
  pages,
  activePageId,
  onAddPage,
  onDeletePage,
  onSelectPage,
  onUpdatePageName,
  onReorderPages,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newPageName, setNewPageName] = useState('');
  const [editingPageId, setEditingPageId] = useState<string | null>(null);
  const [tempPageName, setTempPageName] = useState('');

  useEffect(() => {
    if (editingPageId) {
      const pageToEdit = pages.find(p => p.id === editingPageId);
      if (pageToEdit) {
        setTempPageName(pageToEdit.name);
      }
    }
  }, [editingPageId, pages]);

  const handleAddNewPage = () => {
    if (newPageName.trim()) {
      onAddPage(newPageName.trim());
      setNewPageName('');
      setIsModalOpen(false);
    }
  };

  const handleSavePageName = () => {
    if (editingPageId && tempPageName.trim()) {
      onUpdatePageName(editingPageId, tempPageName.trim());
      setEditingPageId(null);
      setTempPageName('');
    }
  };

  const handleCancelEdit = () => {
    setEditingPageId(null);
    setTempPageName('');
  };

  const handleMovePage = (dragIndex: number, hoverIndex: number) => {
    const draggedPage = pages[dragIndex];
    const newPages = [...pages];
    newPages.splice(dragIndex, 1);
    newPages.splice(hoverIndex, 0, draggedPage);
    onReorderPages(newPages);
  };

  return (
    <div className="h-full w-64 bg-panel-background border-r border-divider-lines p-4 flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-700">Pages</h3>
        <Button variant="ghost" size="sm" className="text-creative-blue hover:text-creative-blue-darker" onClick={() => setIsModalOpen(true)}>
          <PlusCircle size={18} className="mr-1" /> Add
        </Button>
      </div>
      <ul className="flex-1 overflow-auto space-y-2">
        {Array.isArray(pages) && pages.map((page, index) => (
          <DraggablePageItem
            key={page.id}
            page={page}
            index={index}
            isActive={activePageId === page.id}
            isEditing={editingPageId === page.id}
            tempName={tempPageName}
            onSelect={() => onSelectPage(page.id)}
            onDelete={() => onDeletePage(page.id)}
            onStartEdit={() => setEditingPageId(page.id)}
            onSaveEdit={handleSavePageName}
            onCancelEdit={handleCancelEdit}
            onTempNameChange={setTempPageName}
            onMove={handleMovePage}
          />
        ))}
        {Array.isArray(pages) && pages.length === 0 && (
          <li className="p-2 text-center text-sm text-muted-foreground">
            No pages. Click 'Add' to create one.
          </li>
        )}
      </ul>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Page</DialogTitle>
            <DialogDescription>
              Enter a name for your new page. Click create when you're done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newPageName" className="text-right">
                Name
              </Label>
              <Input
                id="newPageName"
                value={newPageName}
                onChange={(e) => setNewPageName(e.target.value)}
                className="col-span-3"
                placeholder="New Page"
                autoFocus
                onKeyDown={(e) => e.key === 'Enter' && handleAddNewPage()}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="button" className="primary-button" onClick={handleAddNewPage}>
              Create Page
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};