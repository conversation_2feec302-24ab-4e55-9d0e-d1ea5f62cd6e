import React, { useEffect } from 'react';

interface KeyboardShortcutsProps {
  onToggleGrid?: () => void;
  onToggleSnapping?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onZoomReset?: () => void;
  onSelectAll?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onPaste?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  onToggleGrid,
  onToggleSnapping,
  onZoomIn,
  onZoomOut,
  onZoomReset,
  onSelectAll,
  onDelete,
  onCopy,
  onPaste,
  onUndo,
  onRedo
}) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs or in modals/dialogs
      const target = event.target as HTMLElement;

      // Check if we're in any text input context
      if (target instanceof HTMLInputElement ||
          target instanceof HTMLTextAreaElement ||
          target instanceof HTMLSelectElement ||
          target?.contentEditable === 'true' ||
          target?.closest('[contenteditable="true"]') ||
          target?.closest('[role="dialog"]') ||
          target?.closest('[data-radix-dialog-content]') || // Radix UI dialog
          target?.closest('.w-64.bg-panel-background') ||
          target?.closest('input') ||
          target?.closest('textarea') ||
          target?.closest('[data-state="open"]') || // Any open modal/dialog
          // Check if any input or textarea is currently focused
          document.activeElement instanceof HTMLInputElement ||
          document.activeElement instanceof HTMLTextAreaElement ||
          document.activeElement?.getAttribute('contenteditable') === 'true' ||
          // Check if we're inside any modal/dialog container
          document.querySelector('[role="dialog"]:not([data-state="closed"])') ||
          document.querySelector('[data-radix-dialog-content]')) {
        return;
      }

      // For space bar and arrow keys, be extra careful - only handle if we're sure we're not in text editing
      if (event.key === ' ' || event.key === 'Spacebar' ||
          event.key === 'ArrowLeft' || event.key === 'ArrowRight' ||
          event.key === 'ArrowUp' || event.key === 'ArrowDown') {
        return; // Never handle these keys globally when they might be used for text editing
      }

      const { key, ctrlKey, metaKey, shiftKey } = event;
      const isModifierPressed = ctrlKey || metaKey;

      switch (key.toLowerCase()) {
        case 'g':
          if (!isModifierPressed) {
            event.preventDefault();
            onToggleGrid?.();
          }
          break;
        
        case 's':
          if (shiftKey && !isModifierPressed) {
            event.preventDefault();
            onToggleSnapping?.();
          }
          break;
        
        case '=':
        case '+':
          if (isModifierPressed) {
            event.preventDefault();
            onZoomIn?.();
          }
          break;
        
        case '-':
          if (isModifierPressed) {
            event.preventDefault();
            onZoomOut?.();
          }
          break;
        
        case '0':
          if (isModifierPressed) {
            event.preventDefault();
            onZoomReset?.();
          }
          break;
        
        case 'a':
          if (isModifierPressed) {
            event.preventDefault();
            onSelectAll?.();
          }
          break;
        
        case 'delete':
        case 'backspace':
          // Only handle delete/backspace if not in any text input and not typing
          if (!isModifierPressed &&
              !(event.target instanceof HTMLInputElement) &&
              !(event.target instanceof HTMLTextAreaElement) &&
              !document.querySelector('input:focus') &&
              !document.querySelector('textarea:focus')) {
            event.preventDefault();
            onDelete?.();
          }
          break;
        
        case 'c':
          if (isModifierPressed) {
            event.preventDefault();
            onCopy?.();
          }
          break;
        
        case 'v':
          if (isModifierPressed) {
            event.preventDefault();
            onPaste?.();
          }
          break;
        
        case 'z':
          if (isModifierPressed) {
            event.preventDefault();
            if (shiftKey) {
              onRedo?.();
            } else {
              onUndo?.();
            }
          }
          break;
        
        case 'y':
          if (isModifierPressed) {
            event.preventDefault();
            onRedo?.();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    onToggleGrid,
    onToggleSnapping,
    onZoomIn,
    onZoomOut,
    onZoomReset,
    onSelectAll,
    onDelete,
    onCopy,
    onPaste,
    onUndo,
    onRedo
  ]);

  return null; // This component doesn't render anything
};

// Helper component to show keyboard shortcuts
export const KeyboardShortcutsHelp: React.FC<{ visible: boolean; onClose: () => void }> = ({
  visible,
  onClose
}) => {
  if (!visible) return null;

  const shortcuts = [
    { key: 'G', description: 'Toggle grid' },
    { key: 'Shift + S', description: 'Toggle grid snapping' },
    { key: 'Ctrl/Cmd + +', description: 'Zoom in' },
    { key: 'Ctrl/Cmd + -', description: 'Zoom out' },
    { key: 'Ctrl/Cmd + 0', description: 'Reset zoom' },
    { key: 'Ctrl/Cmd + A', description: 'Select all' },
    { key: 'Delete/Backspace', description: 'Delete selected' },
    { key: 'Ctrl/Cmd + C', description: 'Copy' },
    { key: 'Ctrl/Cmd + V', description: 'Paste' },
    { key: 'Ctrl/Cmd + Z', description: 'Undo' },
    { key: 'Ctrl/Cmd + Shift + Z', description: 'Redo' },
    { key: '↑ ↓ ← →', description: 'Move selected component' },
    { key: 'Shift + ↑ ↓ ← →', description: 'Move by grid size' },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>
        <div className="space-y-2">
          {shortcuts.map((shortcut, index) => (
            <div key={index} className="flex justify-between items-center py-1">
              <span className="text-sm text-gray-600">{shortcut.description}</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">
                {shortcut.key}
              </kbd>
            </div>
          ))}
        </div>
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Press <kbd className="px-1 bg-gray-100 rounded">?</kbd> to show/hide this help
          </p>
        </div>
      </div>
    </div>
  );
};
