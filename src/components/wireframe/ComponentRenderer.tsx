import React from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';

// Import all component categories
import { commonComponents } from './ui-components/common';
import { layoutComponents } from './ui-components/layouts';
import { formComponents } from './ui-components/forms';
import { textComponents } from './ui-components/text';
import { navigationComponents } from './ui-components/navigation';
import { mediaComponents } from './ui-components/media';
import { dataComponents } from './ui-components/data';
import { interactiveComponents } from './ui-components/interactive';

// Combine all components into a single registry
// Note: Later imports will overwrite earlier ones with the same key
const componentRegistry = {
  ...layoutComponents,
  ...navigationComponents,
  ...mediaComponents,
  ...dataComponents,
  ...interactiveComponents,
  ...textComponents,
  ...formComponents,
  ...commonComponents, // Common components last to take precedence for duplicates
};

// Component registry initialized with all available components

interface ComponentRendererProps {
  component: WireframeComponent;
  isSelected?: boolean;
}

// Fallback component for unknown types
const FallbackComponent: React.FC<ComponentRendererProps> = ({ component, isSelected }) => (
  <div
    style={{
      width: component.width,
      height: component.height,
      border: isSelected ? '2px solid #3b82f6' : '1px solid #d1d5db',
      borderRadius: '4px',
      backgroundColor: '#f9fafb',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px',
      color: '#6b7280',
      textAlign: 'center'
    }}
  >
    {component.text || component.type}
  </div>
);

export const ComponentRenderer: React.FC<ComponentRendererProps> = ({ component, isSelected }) => {
  const ComponentToRender = componentRegistry[component.type as keyof typeof componentRegistry];

  // Debug: Only log if component type is not found
  if (!ComponentToRender) {
    console.log('ComponentRenderer - Missing component type:', {
      componentType: component.type,
      componentId: component.id,
      availableTypes: Object.keys(componentRegistry).slice(0, 10) // Show first 10 types
    });
  }

  if (ComponentToRender) {
    return <ComponentToRender component={component} isSelected={isSelected} />;
  }

  // Fallback to the original simple rendering for unknown types
  console.log('Using fallback component for:', component.type);
  return <FallbackComponent component={component} isSelected={isSelected} />;
};

// Export the list of all available component types for the toolbar
export const getAllComponentTypes = (): string[] => {
  return Object.keys(componentRegistry);
};

// Export component types by category for organized toolbar
export const getComponentsByCategory = () => {
  // Create a set to track which components we've already included
  const seenComponents = new Set<string>();

  const categories = {
    'Common': Object.keys(commonComponents),
    'Layouts': Object.keys(layoutComponents),
    'Form Controls': Object.keys(formComponents),
    'Text': Object.keys(textComponents),
    'Navigation': Object.keys(navigationComponents),
    'Media & Placeholders': Object.keys(mediaComponents),
    'Data & Lists': Object.keys(dataComponents),
    'Interactive Helpers': Object.keys(interactiveComponents)
  };

  // Filter out duplicates, keeping the first occurrence
  const filteredCategories: Record<string, string[]> = {};

  for (const [categoryName, components] of Object.entries(categories)) {
    filteredCategories[categoryName] = components.filter(component => {
      if (seenComponents.has(component)) {
        return false; // Skip duplicate
      }
      seenComponents.add(component);
      return true;
    });
  }

  return filteredCategories;
};
