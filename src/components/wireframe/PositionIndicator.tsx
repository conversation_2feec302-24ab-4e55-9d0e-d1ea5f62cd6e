import React from 'react';

interface PositionIndicatorProps {
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
  canvasWidth: number;
  canvasHeight: number;
  zoom?: number;
  showCoordinates?: boolean;
  showCenterGuides?: boolean;
}

export const PositionIndicator: React.FC<PositionIndicatorProps> = ({
  x,
  y,
  width,
  height,
  visible,
  canvasWidth,
  canvasHeight,
  zoom = 1,
  showCoordinates = true,
  showCenterGuides = true
}) => {
  if (!visible) return null;

  const centerX = x + width / 2;
  const centerY = y + height / 2;
  const canvasCenterX = canvasWidth / 2;
  const canvasCenterY = canvasHeight / 2;

  // Check if element is centered (within tolerance)
  const tolerance = 10 / zoom;
  const isHorizontallyCentered = Math.abs(centerX - canvasCenterX) < tolerance;
  const isVerticallyCentered = Math.abs(centerY - canvasCenterY) < tolerance;

  return (
    <>
      {/* Coordinate Display */}
      {showCoordinates && (
        <div
          style={{
            position: 'absolute',
            left: x,
            top: y - 25,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '11px',
            fontFamily: 'monospace',
            pointerEvents: 'none',
            zIndex: 1001,
            whiteSpace: 'nowrap'
          }}
        >
          X: {Math.round(x)} Y: {Math.round(y)} W: {Math.round(width)} H: {Math.round(height)}
        </div>
      )}

      {/* Center Guide Lines */}
      {showCenterGuides && (
        <>
          {/* Horizontal center line */}
          {isHorizontallyCentered && (
            <div
              style={{
                position: 'absolute',
                left: 0,
                top: canvasCenterY,
                width: canvasWidth,
                height: 1,
                backgroundColor: '#ef4444',
                pointerEvents: 'none',
                zIndex: 999,
                opacity: 0.8
              }}
            />
          )}

          {/* Vertical center line */}
          {isVerticallyCentered && (
            <div
              style={{
                position: 'absolute',
                left: canvasCenterX,
                top: 0,
                width: 1,
                height: canvasHeight,
                backgroundColor: '#ef4444',
                pointerEvents: 'none',
                zIndex: 999,
                opacity: 0.8
              }}
            />
          )}

          {/* Center indicator dot */}
          {(isHorizontallyCentered || isVerticallyCentered) && (
            <div
              style={{
                position: 'absolute',
                left: canvasCenterX - 4,
                top: canvasCenterY - 4,
                width: 8,
                height: 8,
                backgroundColor: '#ef4444',
                borderRadius: '50%',
                pointerEvents: 'none',
                zIndex: 1000,
                opacity: 0.8
              }}
            />
          )}
        </>
      )}

      {/* Alignment guides to other elements */}
      <AlignmentGuides
        x={x}
        y={y}
        width={width}
        height={height}
        canvasWidth={canvasWidth}
        canvasHeight={canvasHeight}
      />
    </>
  );
};

interface AlignmentGuidesProps {
  x: number;
  y: number;
  width: number;
  height: number;
  canvasWidth: number;
  canvasHeight: number;
}

const AlignmentGuides: React.FC<AlignmentGuidesProps> = ({
  x,
  y,
  width,
  height,
  canvasWidth,
  canvasHeight
}) => {
  // This would ideally connect to other elements on the canvas
  // For now, we'll show guides for common alignment points
  
  const guides = [];
  const guideColor = '#3b82f6';
  const guideOpacity = 0.6;

  // Left edge guide
  if (x < 50) {
    guides.push(
      <div
        key="left-guide"
        style={{
          position: 'absolute',
          left: x,
          top: 0,
          width: 1,
          height: canvasHeight,
          backgroundColor: guideColor,
          opacity: guideOpacity,
          pointerEvents: 'none',
          zIndex: 998
        }}
      />
    );
  }

  // Top edge guide
  if (y < 50) {
    guides.push(
      <div
        key="top-guide"
        style={{
          position: 'absolute',
          left: 0,
          top: y,
          width: canvasWidth,
          height: 1,
          backgroundColor: guideColor,
          opacity: guideOpacity,
          pointerEvents: 'none',
          zIndex: 998
        }}
      />
    );
  }

  return <>{guides}</>;
};
