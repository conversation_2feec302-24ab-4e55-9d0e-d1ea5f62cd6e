import React from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';
import { ComponentRenderer } from './ComponentRenderer';

interface DragPreviewProps {
  component: WireframeComponent;
  isDragging: boolean;
  offset?: { x: number; y: number };
}

export const DragPreview: React.FC<DragPreviewProps> = ({
  component,
  isDragging,
  offset = { x: 0, y: 0 }
}) => {
  if (!isDragging) return null;

  return (
    <div
      style={{
        position: 'fixed',
        left: offset.x,
        top: offset.y,
        pointerEvents: 'none',
        zIndex: 10000,
        opacity: 0.8,
        transform: 'rotate(2deg)',
        filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))'
      }}
    >
      <ComponentRenderer component={component} isSelected={false} />
    </div>
  );
};

// Custom drag layer for better visual feedback
export const useDragPreview = () => {
  const [dragState, setDragState] = React.useState<{
    isDragging: boolean;
    component: WireframeComponent | null;
    offset: { x: number; y: number };
  }>({
    isDragging: false,
    component: null,
    offset: { x: 0, y: 0 }
  });

  const startDrag = (component: WireframeComponent, offset: { x: number; y: number }) => {
    setDragState({
      isDragging: true,
      component,
      offset
    });
  };

  const updateDrag = (offset: { x: number; y: number }) => {
    setDragState(prev => ({
      ...prev,
      offset
    }));
  };

  const endDrag = () => {
    setDragState({
      isDragging: false,
      component: null,
      offset: { x: 0, y: 0 }
    });
  };

  return {
    dragState,
    startDrag,
    updateDrag,
    endDrag
  };
};
