import React, { useState, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { getComponentsByCategory } from './ComponentRenderer';
import { useHistoryContext } from '@/contexts/HistoryContext';
import { Undo, Redo } from 'lucide-react';

const componentsByCategory = getComponentsByCategory();
const allComponentTypes = Object.values(componentsByCategory).flat();
const filterTags = ['All', ...Object.keys(componentsByCategory)];

// Helper function to get category icons
const getCategoryIcon = (category: string): string => {
  const icons: Record<string, string> = {
    'All': '📦',
    'Common': '⭐',
    'Layouts': '🖥️',
    'Form Controls': '🔘',
    'Text': '📝',
    'Navigation': '🧭',
    'Media & Placeholders': '🖼️',
    'Data & Lists': '📊',
    'Interactive Helpers': '🎯'
  };

  return icons[category] || '📦';
};

// Helper function to get component icons
const getComponentIcon = (componentType: string): string => {
  const icons: Record<string, string> = {
    // Form Controls
    'Button': '🔘',
    'Text Input': '📝',
    'Text Area': '📄',
    'Checkbox': '☑️',
    'Radio Button': '🔘',
    'Dropdown': '📋',
    'Slider': '🎚️',
    'Progress Bar': '📊',
    'Search Box': '🔍',
    'Date Picker': '📅',
    'Accordion': '📋',
    'Alert Box': '⚠️',
    'Button Bar': '🔘',
    'Color Picker': '🎨',
    'ComboBox': '📝',
    'Field Set': '📦',
    'Checkbox Group': '☑️',

    // Layouts
    'Desktop Browser': '💻',
    'Android Browser': '📱',
    'iOS Browser': '📱',
    'Modal': '🪟',
    'Inspector Panel': '🔧',
    'Browser': '🌐',
    'iPad': '📱',
    'iPhone': '📱',
    'H Splitter': '⬌',
    'H Curly Bracket': '{ }',
    'H Rule': '➖',

    // Navigation
    'Navbar': '🧭',
    'Sidebar': '📑',
    'Tabs': '📂',
    'Pagination': '📄',
    'App Bar': '📱',
    'Breadcrumb': '🍞',
    'Link Bar': '🔗',
    'Menu': '📋',

    // Text
    'Text Label': '🏷️',
    'Text Paragraph': '📝',
    'Squiggly Text': '〰️',

    // Media
    'Image Placeholder': '🖼️',
    'Video Placeholder': '🎥',
    'Icon Placeholder': '⭐',
    'Avatar': '👤',
    'Map Placeholder': '🗺️',
    'Calendar': '📅',
    'Callout': '💬',
    'Comment': '💭',

    // Data
    'Table': '📊',
    'Data Grid': '📋',
    'List': '📝',
    'Bar Chart': '📊',
    'Pie Chart': '🥧',
    'Chart Column': '📊',
    'Chart Line': '📈',
    'Date Chooser': '📅',
    'List with Icons': '📋',

    // Interactive
    'Tooltip': '💬',
    'Sticky Note': '📝',
    'Hotspot': '🎯',
    'Vertical Scrollbar': '📜',
    'Horizontal Scrollbar': '📜',
    'Cursor Arrow': '👆',
    'Connector': '🔗',
    'Help Button': '❓',
    'H Slider': '🎚️',
    'iOS Keyboard': '⌨️',
    'iOS Menu': '📋',
    'iOS Picker': '🎡',
    'Cover Flow': '🎞️',
    'Red X': '❌',
    'Scratch-O': '⚫',
    'Shape': '🔵',
    'Site Map': '🗺️',
    'Smartphone': '📱',
    'Street Map': '🗺️',
    'Tab Bar': '📑',
    'Tag Cloud': '🏷️',
    'Tree Pane': '🌳',
    'Video Player': '📹',
    'Volume Slider': '🔊',
    'Webcam': '📷',

    // New Form Components
    'Menu Bar': '📋',
    'Multiline Button': '🔘',
    'Number Stepper': '🔢',
    'ON/OFF Switch': '🔘',
    'Playback Controls': '⏯️',
    'Pointy Button': '▶️',
    'Time Picker': '🕐',
    'Rectangle': '⬜',

    // New Layout Components
    'V Splitter': '⬍',
    'V Tabs': '📂',
    'V Slider': '🎚️',
    'V Scroll Bar': '📜',
    'V Rule': '│',
    'V Curly Bracket': '{',
    'Window': '🪟',

    // New Text Components
    'Text Title': '📰',
    'Text Subtitle': '📄',
    'Squiggly Line': '〰️',

    // New Navigation Components
    'Toolbar': '🔧',
    'Popover': '💬',
    'Progress Indicator': '📊'
  };

  return icons[componentType] || '📦';
};

// Draggable component item for the toolbar
interface DraggableToolbarItemProps {
  componentType: string;
  onAddComponent: (type: string) => void;
}

const DraggableToolbarItem: React.FC<DraggableToolbarItemProps> = ({
  componentType,
  onAddComponent
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: "TOOLBAR_COMPONENT",
    item: {
      componentType,
      isFromToolbar: true
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`bg-white border border-divider-lines rounded-md flex flex-col items-center justify-center text-xs text-gray-600 cursor-grab hover:bg-gray-50 transition-colors shadow-sm ${
        isDragging ? 'opacity-50 scale-95 shadow-lg' : 'opacity-100 scale-100'
      }`}
      style={{
        width: '72px',  // Fixed width for all components
        height: '56px', // Fixed height for all components
        padding: '4px'
      }}
      onClick={() => onAddComponent(componentType)}
      title={`${componentType} - Click to add or drag to canvas`}
    >
      <div
        className="flex items-center justify-center mb-1"
        style={{
          width: '100%',
          height: '32px',
          fontSize: '18px',
          lineHeight: '18px'
        }}
      >
        {getComponentIcon(componentType)}
      </div>
      <div
        className="text-center leading-tight text-xs font-medium"
        style={{
          width: '100%',
          height: '16px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}
      >
        {componentType.length > 7 ? componentType.substring(0, 7) + '...' : componentType}
      </div>
    </div>
  );
};

interface WireframeToolbarProps {
  onAddComponent: (componentType: string) => void;
}

export const WireframeToolbar: React.FC<WireframeToolbarProps> = ({ onAddComponent }) => {
  const [activeFilter, setActiveFilter] = useState('All');
  const [displayComponentTypes, setDisplayComponentTypes] = useState(allComponentTypes);
  const { undo, redo, canUndo, canRedo } = useHistoryContext();

  useEffect(() => {
    if (activeFilter === 'All') {
      setDisplayComponentTypes(allComponentTypes);
    } else {
      // Filter by category
      const categoryComponents = componentsByCategory[activeFilter as keyof typeof componentsByCategory];
      setDisplayComponentTypes(categoryComponents || []);
    }
  }, [activeFilter]);

  return (
    <div className="h-24 bg-panel-background border-b border-divider-lines flex flex-col">
      {/* Top row: Undo/Redo buttons and Category Filter Tabs - Compact */}
      <div className="flex items-center justify-between px-3 py-1 border-b border-divider-lines bg-gray-50">
        {/* Undo/Redo Controls */}
        <div className="flex items-center space-x-1">
          <Button
            variant="outline"
            size="sm"
            className="h-5 px-2 text-xs"
            onClick={undo}
            disabled={!canUndo}
            title="Undo (Ctrl+Z)"
          >
            <Undo className="h-3 w-3" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-5 px-2 text-xs"
            onClick={redo}
            disabled={!canRedo}
            title="Redo (Ctrl+Y)"
          >
            <Redo className="h-3 w-3" />
          </Button>
        </div>

        {/* Category Filter Tabs - Compact */}
        <div className="flex items-center space-x-1 overflow-x-auto scrollbar-thin flex-1 ml-3">
        {filterTags.map((tag) => (
          <Button
            key={tag}
            variant={activeFilter === tag ? "default" : "outline"}
            size="sm"
            className={cn(
              "text-xs whitespace-nowrap px-2 py-0.5 h-5 min-w-0",
              activeFilter === tag ? "bg-creative-blue text-white hover:bg-creative-blue-darker" : "hover:bg-creative-blue-lighter-bg hover:text-creative-blue"
            )}
            onClick={() => setActiveFilter(tag)}
          >
            <span className="mr-1">{getCategoryIcon(tag)}</span>
            {tag.length > 6 ? tag.substring(0, 6) + '...' : tag}
          </Button>
        ))}
        </div>
      </div>

      {/* Components Grid - More breathing space */}
      <div className="flex-1 overflow-x-auto overflow-y-hidden scrollbar-thin px-3 py-3">
        <div className="flex gap-2 min-w-max h-full items-center">
          {displayComponentTypes.map((comp, index) => (
            <DraggableToolbarItem
              key={`${activeFilter}-${comp}-${index}`}
              componentType={comp}
              onAddComponent={onAddComponent}
            />
          ))}
        </div>
      </div>
    </div>
  );
};