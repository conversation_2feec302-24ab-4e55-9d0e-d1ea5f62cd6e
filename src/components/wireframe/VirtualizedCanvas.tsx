import React, { useMemo, useCallback } from 'react';
import { WireframeComponent } from './WireframePropertiesPanel';
import { ComponentItem } from './ComponentItem';

interface VirtualizedCanvasProps {
  components: WireframeComponent[];
  canvasWidth: number;
  canvasHeight: number;
  zoom: number;
  viewportWidth: number;
  viewportHeight: number;
  scrollLeft: number;
  scrollTop: number;
  selectedComponent: WireframeComponent | null;
  onSelectComponent: (component: WireframeComponent) => void;
  onEditData: (componentId: string) => void;
  onMoveComponent: (id: string, x: number, y: number) => void;
  gridSnapping?: boolean;
  gridSize?: number;
}

// Performance optimization: Only render components that are visible in the viewport
export const VirtualizedCanvas: React.FC<VirtualizedCanvasProps> = ({
  components,
  canvasWidth,
  canvasHeight,
  zoom,
  viewportWidth,
  viewportHeight,
  scrollLeft,
  scrollTop,
  selectedComponent,
  onSelectComponent,
  onEditData,
  onMoveComponent,
  gridSnapping = true,
  gridSize = 20
}) => {
  // Calculate visible area with some buffer for smooth scrolling
  const buffer = 100; // pixels
  const visibleArea = useMemo(() => ({
    left: (scrollLeft / zoom) - buffer,
    top: (scrollTop / zoom) - buffer,
    right: ((scrollLeft + viewportWidth) / zoom) + buffer,
    bottom: ((scrollTop + viewportHeight) / zoom) + buffer
  }), [scrollLeft, scrollTop, viewportWidth, viewportHeight, zoom, buffer]);

  // Filter components that are visible in the viewport
  const visibleComponents = useMemo(() => {
    return components.filter(component => {
      const componentRight = component.x + component.width;
      const componentBottom = component.y + component.height;

      return !(
        component.x > visibleArea.right ||
        componentRight < visibleArea.left ||
        component.y > visibleArea.bottom ||
        componentBottom < visibleArea.top
      );
    });
  }, [components, visibleArea]);

  // Memoized component renderer
  const renderComponent = useCallback((component: WireframeComponent) => (
    <ComponentItem
      key={component.id}
      component={component}
      onSelect={onSelectComponent}
      onDoubleClick={() => onEditData(component.id)}
      isSelected={selectedComponent?.id === component.id}
      gridSnapping={gridSnapping}
      gridSize={gridSize}
      onMove={onMoveComponent}
    />
  ), [selectedComponent, onSelectComponent, onEditData, onMoveComponent, gridSnapping, gridSize]);

  return (
    <>
      {visibleComponents.map(renderComponent)}
      
      {/* Performance indicator for development */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'absolute',
            top: 10,
            left: 10,
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '10px',
            fontFamily: 'monospace',
            pointerEvents: 'none',
            zIndex: 1000
          }}
        >
          Rendering: {visibleComponents.length} / {components.length} components
        </div>
      )}
    </>
  );
};

// Hook for managing canvas viewport
export const useCanvasViewport = (canvasRef: React.RefObject<HTMLDivElement>) => {
  const [viewport, setViewport] = React.useState({
    width: 0,
    height: 0,
    scrollLeft: 0,
    scrollTop: 0
  });

  const updateViewport = useCallback(() => {
    if (canvasRef.current) {
      const { clientWidth, clientHeight, scrollLeft, scrollTop } = canvasRef.current;
      setViewport({
        width: clientWidth,
        height: clientHeight,
        scrollLeft,
        scrollTop
      });
    }
  }, [canvasRef]);

  React.useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Initial measurement
    updateViewport();

    // Listen for scroll and resize events
    const handleScroll = () => updateViewport();
    const handleResize = () => updateViewport();

    canvas.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize);

    // Use ResizeObserver if available for more accurate canvas size tracking
    let resizeObserver: ResizeObserver | null = null;
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(canvas);
    }

    return () => {
      canvas.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [canvasRef, updateViewport]);

  return viewport;
};

// Performance monitoring hook
export const usePerformanceMonitor = (componentCount: number) => {
  const [metrics, setMetrics] = React.useState({
    renderTime: 0,
    frameRate: 0,
    memoryUsage: 0
  });

  React.useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measurePerformance = () => {
      const currentTime = performance.now();
      frameCount++;

      // Update metrics every second
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        setMetrics(prev => ({
          ...prev,
          frameRate: fps,
          memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
        }));

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measurePerformance);
    };

    if (process.env.NODE_ENV === 'development') {
      animationId = requestAnimationFrame(measurePerformance);
    }

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [componentCount]);

  return metrics;
};
