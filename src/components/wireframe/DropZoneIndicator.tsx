import React from 'react';

interface DropZoneIndicatorProps {
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
  zoom?: number;
  type?: 'valid' | 'invalid' | 'preview';
}

export const DropZoneIndicator: React.FC<DropZoneIndicatorProps> = ({
  x,
  y,
  width,
  height,
  visible,
  zoom = 1,
  type = 'valid'
}) => {
  if (!visible) return null;

  const getStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      left: x,
      top: y,
      width,
      height,
      borderRadius: '4px',
      pointerEvents: 'none' as const,
      zIndex: 1000,
      transition: 'all 0.2s ease-in-out'
    };

    switch (type) {
      case 'valid':
        return {
          ...baseStyles,
          border: '2px dashed #10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          boxShadow: '0 0 0 1px rgba(16, 185, 129, 0.2)'
        };
      case 'invalid':
        return {
          ...baseStyles,
          border: '2px dashed #ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          boxShadow: '0 0 0 1px rgba(239, 68, 68, 0.2)'
        };
      case 'preview':
        return {
          ...baseStyles,
          border: '2px dashed #3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.2)'
        };
      default:
        return baseStyles;
    }
  };

  return (
    <div style={getStyles()}>
      {/* Corner indicators */}
      <div
        style={{
          position: 'absolute',
          top: -4,
          left: -4,
          width: 8,
          height: 8,
          backgroundColor: type === 'valid' ? '#10b981' : type === 'invalid' ? '#ef4444' : '#3b82f6',
          borderRadius: '50%'
        }}
      />
      <div
        style={{
          position: 'absolute',
          top: -4,
          right: -4,
          width: 8,
          height: 8,
          backgroundColor: type === 'valid' ? '#10b981' : type === 'invalid' ? '#ef4444' : '#3b82f6',
          borderRadius: '50%'
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: -4,
          left: -4,
          width: 8,
          height: 8,
          backgroundColor: type === 'valid' ? '#10b981' : type === 'invalid' ? '#ef4444' : '#3b82f6',
          borderRadius: '50%'
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: -4,
          right: -4,
          width: 8,
          height: 8,
          backgroundColor: type === 'valid' ? '#10b981' : type === 'invalid' ? '#ef4444' : '#3b82f6',
          borderRadius: '50%'
        }}
      />
    </div>
  );
};
