import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import { WireframeComponent } from './WireframePropertiesPanel'; // Path to WireframeComponent type
import { ComponentRenderer } from './ComponentRenderer';
import { snapToGrid } from './GridOverlay';

// Define a clear type for the item being dragged and dropped by react-dnd
export interface DndComponentItemPayload {
  id: string;
  x: number;
  y: number;
  type: "COMPONENT"; // Literal type for dnd operations, matches useDrag/useDrop type
}

// Interface for toolbar component drag items
export interface DndToolbarItemPayload {
  componentType: string;
  isFromToolbar: boolean;
}

// Union type for all drag items
export type DragItem = DndComponentItemPayload | DndToolbarItemPayload;

interface ComponentItemProps {
  component: WireframeComponent;
  onSelect: (component: WireframeComponent) => void;
  onDoubleClick: (component: WireframeComponent) => void;
  isSelected?: boolean;
  gridSnapping?: boolean;
  gridSize?: number;
  onMove?: (id: string, x: number, y: number) => void;
}

export const ComponentItem: React.FC<ComponentItemProps> = ({
  component,
  onSelect,
  onDoubleClick,
  isSelected = false,
  gridSnapping = true,
  gridSize = 20,
  onMove
}) => {
  const [isDragHovering, setIsDragHovering] = useState(false);

  const [{ isDragging }, drag] = useDrag(() => ({
    type: "COMPONENT", // This type must match the 'accept' type in useDrop
    item: { id: component.id, x: component.x, y: component.y, type: "COMPONENT" } as DndComponentItemPayload,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (dropResult && onMove) {
        let newX = dropResult.x || component.x;
        let newY = dropResult.y || component.y;

        // Apply grid snapping if enabled
        if (gridSnapping) {
          newX = snapToGrid(newX, gridSize);
          newY = snapToGrid(newY, gridSize);
        }

        onMove(component.id, newX, newY);
      }
    }
  }));

  return (
    <div
      ref={drag}
      className={`absolute transition-all duration-200 ${
        isDragging ? 'cursor-grabbing' : 'cursor-grab'
      } ${
        isSelected ? 'z-10' : 'z-0'
      } ${
        isDragHovering ? 'scale-105' : 'scale-100'
      }`}
      style={{
        left: `${component.x}px`,
        top: `${component.y}px`,
        opacity: isDragging ? 0.3 : 1,
        filter: isDragging ? 'blur(1px)' : 'none',
        transform: `scale(${isDragHovering ? 1.02 : 1})`,
      }}
      onClick={() => onSelect(component)}
      onDoubleClick={() => onDoubleClick(component)}
      onMouseEnter={() => setIsDragHovering(true)}
      onMouseLeave={() => setIsDragHovering(false)}
      data-component-id={component.id}
    >
      <ComponentRenderer component={component} isSelected={isSelected} />

      {/* Enhanced selection indicator */}
      {isSelected && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            border: '2px solid #3b82f6',
            borderRadius: '4px',
            boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.2)',
            zIndex: 1
          }}
        >
          {/* Selection handles */}
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 border border-white rounded-full"></div>
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 border border-white rounded-full"></div>
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 border border-white rounded-full"></div>
          <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 border border-white rounded-full"></div>
        </div>
      )}
    </div>
  );
};