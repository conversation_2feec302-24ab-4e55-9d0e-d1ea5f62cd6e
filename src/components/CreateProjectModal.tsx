import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { showSuccess, showError } from "@/utils/toast";

interface CreateProjectModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onProjectCreated: (projectName: string) => void; // New prop
}

export const CreateProjectModal: React.FC<CreateProjectModalProps> = ({
  isOpen,
  onOpenChange,
  onProjectCreated, // Destructure new prop
}) => {
  const [projectName, setProjectName] = useState("");
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedProjectName = projectName.trim();
    if (!trimmedProjectName) {
      showError("Project name cannot be empty.");
      return;
    }

    // Call the callback to update the projects list in ProjectsPage
    onProjectCreated(trimmedProjectName);

    // Use a slug-like ID for navigation, consistent with ProjectsPage
    const projectId = encodeURIComponent(trimmedProjectName.toLowerCase().replace(/\s+/g, '-'));

    showSuccess(`Project "${trimmedProjectName}" created!`);
    onOpenChange(false); // Close the modal
    setProjectName(""); // Reset input
    navigate(`/flowchart/${projectId}`); // Navigate to the flowchart page
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Enter a name for your new project. Click create when you're done.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="projectName" className="text-right">
                Name
              </Label>
              <Input
                id="projectName"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                className="col-span-3"
                placeholder="My Awesome Project"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" className="primary-button">
              Create Project
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};