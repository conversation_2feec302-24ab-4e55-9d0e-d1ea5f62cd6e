import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";

const navigationItems = [
  { name: "Projects", href: "/projects" },
  { name: "Flowchart", href: "/flowchart" },
  { name: "Wireframe", href: "/wireframe" },
];

export const NavigationBar = () => {
  return (
    <nav className="fixed top-0 left-0 right-0 h-16 bg-white shadow-md flex items-center justify-between px-6 z-50">
      <div className="flex items-center space-x-8">
        <NavLink to="/" className="text-2xl font-bold text-creative-blue">
          MKDWire
        </NavLink>
        {navigationItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              cn(
                "text-gray-600 hover:text-creative-blue px-3 py-2 rounded-md text-sm font-medium",
                isActive ? "text-creative-blue bg-creative-blue-lighter-bg" : ""
              )
            }
          >
            {item.name}
          </NavLink>
        ))}
      </div>
      {/* Add user profile/settings icon on the right if needed later */}
    </nav>
  );
};