import React, { createContext, useContext, ReactNode } from 'react';
import { useHistory, HistoryAction, HistoryState, UseHistoryOptions } from '@/hooks/useHistory';

interface HistoryContextType<T = any> {
  addAction: (action: Omit<HistoryAction<T>, 'timestamp'>) => void;
  addDebouncedAction: (action: Omit<HistoryAction<T>, 'timestamp'>) => void;
  undo: () => boolean;
  redo: () => boolean;
  clear: () => void;
  canUndo: boolean;
  canRedo: boolean;
  history: HistoryAction<T>[];
  currentIndex: number;
  lastAction?: HistoryAction<T>;
  getHistoryState: () => HistoryState<T>;
}

const HistoryContext = createContext<HistoryContextType | null>(null);

interface HistoryProviderProps {
  children: ReactNode;
  options?: UseHistoryOptions;
}

export const HistoryProvider: React.FC<HistoryProviderProps> = ({ 
  children, 
  options = {} 
}) => {
  const historyManager = useHistory(options);

  return (
    <HistoryContext.Provider value={historyManager}>
      {children}
    </HistoryContext.Provider>
  );
};

export const useHistoryContext = <T = any>(): HistoryContextType<T> => {
  const context = useContext(HistoryContext);
  if (!context) {
    throw new Error('useHistoryContext must be used within a HistoryProvider');
  }
  return context as HistoryContextType<T>;
};

// Hook for keyboard shortcuts
export const useHistoryKeyboardShortcuts = () => {
  const { undo, redo, canUndo, canRedo } = useHistoryContext();

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for Ctrl+Z (undo) or Cmd+Z on Mac
      if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        if (canUndo) {
          undo();
        }
      }
      
      // Check for Ctrl+Y (redo) or Ctrl+Shift+Z or Cmd+Shift+Z on Mac
      if (
        ((event.ctrlKey || event.metaKey) && event.key === 'y') ||
        ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'Z')
      ) {
        event.preventDefault();
        if (canRedo) {
          redo();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo, canUndo, canRedo]);

  return { undo, redo, canUndo, canRedo };
};
