@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 204 20% 98%; /* #F9FAFB */
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217 91% 60%; /* #2F80ED - Calm Creative Blue */
    --primary-foreground: 210 40% 98%; /* White */

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%; /* #E0E0E0 - Divider Lines */
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem; /* Corresponds to 6px for buttons if base font is 16px */

    /* Custom colors from the brief */
    --app-background: theme('colors.slate.100'); /* #F9FAFB */
    --side-toolbar-background: theme('colors.white'); /* #FFFFFF */
    --panel-background: 220 14% 96%; /* #F0F2F5 - Soft gray for contrast */
    --canvas-background: theme('colors.white'); /* #FFFFFF */
    --divider-lines: theme('colors.gray.300'); /* #E0E0E0 */
    
    --creative-blue: #2F80ED;
    --creative-blue-darker: #1C63D5;
    --creative-blue-lighter-bg: #F3F9FF;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%; /* #2F80ED */
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Custom dark colors - adjust as needed */
    --app-background: theme('colors.slate.900');
    --side-toolbar-background: theme('colors.slate.800');
    --panel-background: theme('colors.slate.700');
    --canvas-background: theme('colors.slate.800');
    --divider-lines: theme('colors.slate.600');

    --creative-blue: #2F80ED;
    --creative-blue-darker: #1C63D5;
    --creative-blue-lighter-bg: theme('colors.slate.700'); /* Adjust for dark mode */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans; /* Updated to use standard theme variables */
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    /* Example: Apply Clash Display or Satoshi if added */
    /* font-family: 'Clash Display', sans-serif; */
  }
}

@layer components {
  .primary-button {
    @apply bg-primary text-primary-foreground hover:bg-creative-blue-darker font-semibold py-2 px-4 rounded-md;
    font-size: 15px; /* 14-16px range */
  }

  .secondary-button {
    @apply border border-primary text-primary bg-white hover:bg-creative-blue-lighter-bg font-semibold py-2 px-4 rounded-md;
    font-size: 15px; /* 14-16px range */
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Drag and drop animations */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-fadeIn {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-slideIn {
    animation: slideIn 0.3s ease-out;
  }

  /* Enhanced drag states */
  .dragging {
    transform: rotate(2deg) scale(1.05);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
    z-index: 1000;
  }

  .drag-preview {
    pointer-events: none;
    transform: rotate(2deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  }
}